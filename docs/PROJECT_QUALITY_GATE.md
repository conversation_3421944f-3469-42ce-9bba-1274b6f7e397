# Project Quality Gate Management

This document describes the project quality gate functionality that allows creating and managing quality gates at the project level.

## Overview

The project quality gate system enforces a business rule that **each project can have only one quality gate**. This is different from sprint-level quality gates which can have multiple instances per project.

## Key Features

- ✅ **One quality gate per project** - Enforced by unique constraint
- ✅ **Store without return** - Function stores data but doesn't return the created record
- ✅ **Automatic upsert** - Updates existing quality gate or creates new one
- ✅ **Data validation** - Validates all input parameters
- ✅ **Transaction safety** - All operations are database transaction safe

## Database Schema

The quality gates table supports both project-level and sprint-level quality gates:

- **Project-level**: `sprint_id` is `NULL`
- **Sprint-level**: `sprint_id` has a value

A unique constraint ensures only one project-level quality gate per project.

## Usage

### Basic Usage

```php
use App\Interfaces\QualityGateServiceInterface;

class YourController extends Controller
{
    private QualityGateServiceInterface $qualityGateService;

    public function __construct(QualityGateServiceInterface $qualityGateService)
    {
        $this->qualityGateService = $qualityGateService;
    }

    public function createQualityGate(Request $request)
    {
        $data = [
            'project_id' => 1,
            'quality_gate' => 2, // 0=N/A, 1=Fail, 2=Pass
            'number_of_non_compliance' => 0,
            'number_of_process' => 5,
            'number_of_incident' => 1,
            'number_of_customer_complaint' => 0,
            'note' => 'Project quality gate assessment'
        ];

        // This stores the quality gate but does NOT return the created record
        $this->qualityGateService->storeProjectQualityGate($data);
        
        // Success - no return value
    }
}
```

### Checking if Project Has Quality Gate

```php
$projectId = 1;
$hasQualityGate = $this->qualityGateService->hasProjectQualityGate($projectId);

if ($hasQualityGate) {
    echo "Project already has a quality gate";
}
```

### Retrieving Project Quality Gate

```php
$projectId = 1;
$qualityGate = $this->qualityGateService->getProjectQualityGate($projectId);

if ($qualityGate) {
    echo "Quality Gate Status: " . $qualityGate->quality_gate;
    echo "Note: " . $qualityGate->note;
}
```

## Data Structure

### Required Fields
- `project_id` (integer) - Must be a valid project ID

### Optional Fields
- `quality_gate` (integer) - 0=N/A, 1=Fail, 2=Pass (default: 0)
- `number_of_non_compliance` (integer) - Default: 0
- `number_of_process` (integer) - Default: 0
- `number_of_incident` (integer) - Default: 0
- `number_of_customer_complaint` (integer) - Default: 0
- `note` (string) - Optional notes

## Business Rules

1. **One Per Project**: Each project can have only one project-level quality gate
2. **Automatic Update**: If a quality gate already exists for a project, it will be updated instead of creating a duplicate
3. **No Return Value**: The `storeProjectQualityGate` method intentionally does not return the created/updated record
4. **Validation**: All input data is validated before storage

## Error Handling

The service throws specific exceptions for different error conditions:

```php
try {
    $this->qualityGateService->storeProjectQualityGate($data);
} catch (\InvalidArgumentException $e) {
    // Handle validation errors (missing project_id, invalid values, etc.)
} catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
    // Handle case where project doesn't exist
} catch (\Exception $e) {
    // Handle other database or system errors
}
```

## Database Migration

To enable this functionality, run the migration:

```bash
php artisan migrate
```

This will:
1. Make `sprint_id` nullable in the quality_gates table
2. Add a unique constraint for project-level quality gates

## Testing

Run the unit tests to verify functionality:

```bash
php artisan test tests/Unit/QualityGateServiceTest.php
```

## API Example

If using the provided controller, you can make HTTP requests:

```bash
# Create/Update project quality gate
POST /api/projects/{projectId}/quality-gate
Content-Type: application/json

{
    "project_id": 1,
    "quality_gate": 2,
    "number_of_non_compliance": 0,
    "number_of_process": 5,
    "note": "All processes completed successfully"
}

# Check if project has quality gate
GET /api/projects/{projectId}/quality-gate/check

# Get project quality gate
GET /api/projects/{projectId}/quality-gate
```

## Implementation Notes

- The service uses Laravel's `updateOrCreate` method to handle the uniqueness constraint
- All database operations should be wrapped in transactions
- The service follows the repository pattern used throughout the codebase
- Dependency injection is used for better testability and maintainability
