<?php

namespace Tests\Unit;

use App\Models\Project;
use App\Models\QualityGate;
use App\Services\QualityGateService;
use App\Repositories\QualityGateRepository;
use App\Repositories\ProjectRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use InvalidArgumentException;
use Tests\TestCase;

class QualityGateServiceTest extends TestCase
{
    use RefreshDatabase;

    private QualityGateService $qualityGateService;
    private Project $project;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->qualityGateService = app(QualityGateService::class);
        $this->project = Project::factory()->create();
    }

    /** @test */
    public function it_can_store_project_quality_gate()
    {
        $data = [
            'project_id' => $this->project->id,
            'quality_gate' => 2, // Pass
            'number_of_non_compliance' => 0,
            'number_of_process' => 5,
            'number_of_incident' => 1,
            'number_of_customer_complaint' => 0,
            'note' => 'Test quality gate'
        ];

        // Should not throw any exception
        $this->qualityGateService->storeProjectQualityGate($data);

        // Verify the quality gate was stored
        $this->assertTrue($this->qualityGateService->hasProjectQualityGate($this->project->id));
        
        $qualityGate = $this->qualityGateService->getProjectQualityGate($this->project->id);
        $this->assertNotNull($qualityGate);
        $this->assertEquals($this->project->id, $qualityGate->project_id);
        $this->assertNull($qualityGate->sprint_id); // Should be null for project-level
        $this->assertEquals(2, $qualityGate->quality_gate);
    }

    /** @test */
    public function it_enforces_uniqueness_constraint()
    {
        $data = [
            'project_id' => $this->project->id,
            'quality_gate' => 1,
            'note' => 'First quality gate'
        ];

        // Store first quality gate
        $this->qualityGateService->storeProjectQualityGate($data);

        // Store second quality gate for same project (should update, not create new)
        $data['quality_gate'] = 2;
        $data['note'] = 'Updated quality gate';
        $this->qualityGateService->storeProjectQualityGate($data);

        // Should still have only one quality gate
        $qualityGates = QualityGate::where('project_id', $this->project->id)
            ->whereNull('sprint_id')
            ->get();
        
        $this->assertCount(1, $qualityGates);
        $this->assertEquals(2, $qualityGates->first()->quality_gate);
        $this->assertEquals('Updated quality gate', $qualityGates->first()->note);
    }

    /** @test */
    public function it_throws_exception_for_invalid_project_id()
    {
        $this->expectException(ModelNotFoundException::class);
        
        $data = [
            'project_id' => 99999, // Non-existent project
            'quality_gate' => 1
        ];

        $this->qualityGateService->storeProjectQualityGate($data);
    }

    /** @test */
    public function it_throws_exception_for_missing_project_id()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Project ID is required');
        
        $data = [
            'quality_gate' => 1
        ];

        $this->qualityGateService->storeProjectQualityGate($data);
    }

    /** @test */
    public function it_throws_exception_for_invalid_quality_gate_value()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid quality gate value');
        
        $data = [
            'project_id' => $this->project->id,
            'quality_gate' => 5 // Invalid value
        ];

        $this->qualityGateService->storeProjectQualityGate($data);
    }

    /** @test */
    public function it_sets_default_values()
    {
        $data = [
            'project_id' => $this->project->id
        ];

        $this->qualityGateService->storeProjectQualityGate($data);

        $qualityGate = $this->qualityGateService->getProjectQualityGate($this->project->id);
        
        $this->assertEquals(0, $qualityGate->quality_gate); // Default N/A
        $this->assertEquals(0, $qualityGate->number_of_non_compliance);
        $this->assertEquals(0, $qualityGate->number_of_process);
        $this->assertEquals(0, $qualityGate->number_of_incident);
        $this->assertEquals(0, $qualityGate->number_of_customer_complaint);
    }

    /** @test */
    public function it_returns_null_for_non_existent_quality_gate()
    {
        $qualityGate = $this->qualityGateService->getProjectQualityGate($this->project->id);
        $this->assertNull($qualityGate);
        
        $hasQualityGate = $this->qualityGateService->hasProjectQualityGate($this->project->id);
        $this->assertFalse($hasQualityGate);
    }
}
