FROM wyveo/nginx-php-fpm:php81
RUN apt-get update  && apt-get install -y netcat curl &&  apt-get install -y \
&& apt-get purge -y --auto-remove $buildDeps \
    && apt-get clean \
    && apt-get autoremove \
    && rm -rf /var/lib/apt/lists/*
COPY docker/supervisord.conf /etc/supervisord.conf
COPY docker/start.sh /start.sh
RUN chmod 755 /start.sh
WORKDIR /usr/share/nginx/html
COPY composer.json ./
COPY composer.lock ./
RUN composer install -vv --no-interaction --no-scripts --no-dev
COPY . .
RUN chmod 777 -R ./docker
COPY .env.example ./.env
RUN composer dump-autoload --no-dev
RUN chmod 777 -R /usr/share/nginx/html/storage


