version: '3'
services:
  project:
    build:
      context: .
      dockerfile: docker/Dockerfilephp80
    image: project
    restart: unless-stopped
    tty: true
    environment:
      CONTAINER_ROLE: app
      SERVICE_NAME: app
      SERVICE_TAGS: dev
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - app-network
  webserver:
    image: nginx:alpine
    restart: unless-stopped
    tty: true
    ports:
      - ${APP_PORT}:80
    volumes:
      - ./:/var/www
      - ./docker/nginx/app.d/:/etc/nginx/conf.d/
    networks:
      - app-network
  queue:
    image: project
    depends_on:
      - project
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    environment:
      CONTAINER_ROLE: queue
  #MySQL Service
  db:
    image: mysql:8.0.31
    # container_name: db
    restart: unless-stopped
    tty: true
    ports:
      - ${MYSQL_PORT}:3306
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - dbdata:/var/lib/mysql/
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - app-network

#Docker Networks
networks:
  app-network:
    driver: bridge
#Volumes
volumes:
  dbdata:
    driver: local
