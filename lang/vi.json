{"google_sign_in_successful": "google đăng nh<PERSON>p thành công", "google_sign_in_email_existed": "email đăng nhập google đã tồn tại", "email_or_password_is_incorrect": "Xin lỗi, email hoặc mật khẩu không chính xác", "login_successful": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "google_sign_in_failed": "<PERSON><PERSON><PERSON> nhập google không thành công", "facebook_sign_in_failed": "đ<PERSON><PERSON> nhập facebook không thành công", "success": "Th<PERSON>nh công !", "fail": "<PERSON>h<PERSON>t bại !", "file_max": "số lượ<PERSON> tệp không đ<PERSON><PERSON><PERSON> n<PERSON> hơn 20", "file_required": "<PERSON><PERSON><PERSON><PERSON> có tệp nào đ<PERSON><PERSON><PERSON> tải lên", "file_mimes": "<PERSON>ệ<PERSON> đã tải lên không đúng định dạng", "plain_text_empty": "<PERSON><PERSON> lỗi, không thể để trống văn bản", "notification_not_found": "<PERSON>n lỗi, không tìm thấy thông báo", "unauthorize": "<PERSON>hông đư<PERSON> phép !", "signature_verification_failed": "<PERSON><PERSON><PERSON> minh chữ ký không thành công!", "expired_token": "<PERSON>ã thông báo đã hết hạn!", "project_name_required": "<PERSON>n lỗi, không được để trống tên dự án", "stage_version_already_exist": "Hiện đã tồn tại phiên bản này. <PERSON><PERSON> lòng nhập số phiên bản khác", "expected_date_is_between_the_end_date_stage": "Thời gian dự kiến của milestone hoặc delivery phải nằm trong thời gian stage. <PERSON><PERSON> lòng chọn lại", "expected_date_is_less_than_the_end_date_stage": "Thời gian dự kiến phải nằm trong thời gian diễn ra stage. <PERSON><PERSON> lòng chọn lại", "expected_allocate_between_range_stage": "Allocate nhân sự đang nằm ngoài khoảng thời gian replan, vui lòng thay đổi ngày allocate trướ<PERSON> khi replan stage", "only_projects_of_the_opportunity_type_deleted": "Chỉ xóa đư<PERSON>c dự án thuộc loại opportunity", "following_exp_time_greater_than_previous_milestone": "Thời gian dự kiến của milestone sau phải lớn hơn thời gian dự kiến của milestone trước", "following_exp_time_lesser_than_previous_milestone": "Thời gian dự kiến của milestone trước phải nhỏ hơn thời gian dự kiến của milestone sau", "expected_date_is_between_the_end_date_milestone": "Thời gian dự kiến của milestone phải nằm trong thời gian giữa 2 mislestone. <PERSON><PERSON> lòng chọn lại", "following_exp_time_greater_than_previous_delivery": "Thời gian dự kiến của delivery sau phải lớn hơn thời gian dự kiến của delivery trước", "following_exp_time_lesser_than_previous_delivery": "Thời gian dự kiến của delivery trước phải nhỏ hơn thời gian dự kiến của delivery sau", "expected_date_is_between_the_end_date_delivery": "Thời gian dự kiến của allocation phải nằm trong thời gian stage. <PERSON><PERSON> lòng chọn lại", "user_is_not_the_project_please_add_members_to_the_project": ":user, nhân sự chưa đư<PERSON>c thêm vào dự án. <PERSON><PERSON><PERSON> thêm các nhân sự này vào mới có thể thêm resource. Bạn có muốn thêm vào ngay không?", "time_allocate_within_the_span_of_stage": "Thời gian Allocate phải nằm trong khoảng thời gian của Stage", "activity_update_project": ":user đã chỉnh sửa thông tin của dự án :project", "activity_add_member": ":member đ<PERSON> đ<PERSON><PERSON><PERSON> thêm vào dự án :project bởi :user.", "activity_remove_member": ":member đ<PERSON> đư<PERSON><PERSON> xó<PERSON> khỏi dự án :project bởi :user.", "stage_added": ":user đã tạo stage :name", "stage_updated": ":user đã chỉnh sửa stage :name", "stage_deleted": ":user đã xóa stage :name", "milestone_added": ":user đã thêm milestone :name", "milestone_updated": ":user đã chỉnh sửa thông tin của milestone :name", "milestone_deleted": ":user đã xóa milestone :name", "deliverable_added": ":user đã thêm deliverable :name", "deliverable_updated": ":user đã chỉnh sửa thông tin của deliverable :name", "deliverable_deleted": ":user đã xóa deliverable :name", "allocation_added": ":user đã thêm allocation cho thành viên :member", "allocation_updated": ":user đã cập nhật allocation của thành viên :member", "allocation_deleted": ":user đã xóa allocation của thành viên :member", "violation_report": ":name", "exists_allocation": "Nhân sự đang đã có resource trong dự án. <PERSON><PERSON> lòng xóa toàn bộ resource trước", "require": "<PERSON><PERSON> lòng nhập dữ liệu", "not_found": "Not found!", "not_owner": "Not Owner"}