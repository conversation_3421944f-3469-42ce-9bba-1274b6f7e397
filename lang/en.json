{"google_sign_in_successful": "google sign in successful", "google_sign_in_email_existed": "google sign in email existed", "email_or_password_is_incorrect": "Sorry, email or password is incorrect", "login_successful": "login successful", "google_sign_in_failed": "google sign in failed", "facebook_sign_in_failed": "facebook sign in failed", "success": "Success !", "fail": "Fail !", "file_max": "number of files should not be more than 20", "file_required": "No files have been uploaded", "file_mimes": "The uploaded file is not in the correct format", "plain_text_empty": "Sorry, plain text can't be empty.", "notification_not_found": "Sorry, notice not found.", "unauthorize": "Unauthorize !", "signature_verification_failed": "Signature verification failed !", "expired_token": "Token expired !", "project_name_required": "Sorry, project name can't be empty.", "stage_version_already_exist": "This version already exists. Please enter another version number.", "expected_date_is_less_than_the_end_date_stage": " The estimated time must be within the time of the stage. Please choose again", "only_projects_of_the_opportunity_type_deleted": "Only projects of the opportunity type can be deleted", "following_exp_time_greater_than_previous_milestone": "The estimated time of the following milestone must be greater than the estimated time of the previous milestone", "following_exp_time_lesser_than_previous_milestone": "The estimated time of the following milestone must be lesser than the estimated time of the previous milestone", "expected_date_is_between_the_end_date_milestone": "The estimated time must be within the time of the milestone. Please choose again", "following_exp_time_greater_than_previous_delivery": "The estimated time of the following delivery must be greater than the estimated time of the previous delivery", "following_exp_time_lesser_than_previous_delivery": "The estimated time of the following delivery must be lesser than the estimated time of the previous delivery", "expected_date_is_between_the_end_date_delivery": "The estimated time must be within the time of the delivery. Please choose again", "user_is_not_the_project_please_add_members_to_the_project": "User is not part of the project. Please add members to the project", "time_allocate_within_the_span_of_stage": "Time allocate within the span of stage"}