<?php

namespace App\Enums;

class EContractType
{
    public const LABO = 1;

    public const FIXED_PRICE = 2;

    public const REVENUE_SHARE = 3;

    public static function getAll()
    {
        return [
            self::LABO,
            self::FIXED_PRICE,
            self::REVENUE_SHARE,
        ];
    }

    public static function getMasterData()
    {
        return [
            'labo' => self::LABO,
            'fixed_price' => self::FIXED_PRICE,
            'revenue_share' => self::REVENUE_SHARE,
        ];
    }

    public static function getActionString($value)
    {
        switch ($value) {
            case EContractType::LABO:
                return 'labo';
            case EContractType::FIXED_PRICE:
                return 'fix price';
            case EContractType::REVENUE_SHARE:
                return 'revenue share';
        }

        return null;
    }
}
