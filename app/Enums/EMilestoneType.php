<?php

namespace App\Enums;

class EMilestoneType
{
    public const ANALYSIS = 1;

    public const DESIGN = 2;

    public const DEVELOPMENT = 3;

    public const TESTING = 4;

    public static function getAll()
    {
        return [
            self::ANALYSIS,
            self::DESIGN,
            self::DEVELOPMENT,
            self::TESTING,
        ];
    }

    public static function getMasterData()
    {
        return [
            'analysis' => self::ANALYSIS,
            'design' => self::DESIGN,
            'development' => self::DEVELOPMENT,
            'testing' => self::TESTING,
        ];
    }

    public static function getActionString($value)
    {
        switch ($value) {
            case EMilestoneType::ANALYSIS:
                return 'Analysis';
            case EMilestoneType::DESIGN:
                return 'Design';
            case EMilestoneType::DEVELOPMENT:
                return 'Development';
            case EMilestoneType::TESTING:
                return 'Testing';
        }

        return null;
    }
}
