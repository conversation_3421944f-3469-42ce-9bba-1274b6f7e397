<?php

namespace App\Enums;

class EStaffContractType
{
    const FIXED_TERM_CONTRACT = 1;
    const INDEFINITE_TERM_CONTRACT = 2;
    const PROBATIONARY_CONTRACT = 3;
    const APPRENTICESHIP_CONTRACT = 4;
    const RENTAL_CONTRACT = 5;
    const INTERNSHIP_CONTRACT = 6;
    const PART_TIME_CONTRACT = 9;

    const CONTRACT_TYPES = [
        self::FIXED_TERM_CONTRACT => 'Có thời hạn',
        self::INDEFINITE_TERM_CONTRACT => 'Không thời hạn',
        self::PROBATIONARY_CONTRACT => 'Thử việc',
        self::APPRENTICESHIP_CONTRACT => 'Học việc',
        self::RENTAL_CONTRACT => 'Thuê khoán',
        self::INTERNSHIP_CONTRACT => 'Thực tập',
        self::PART_TIME_CONTRACT => 'Part time',
    ];

    public static function convertContractType($contractType): string
    {
        return self::CONTRACT_TYPES[$contractType] ?? '';
    }

    const CONTRACT_TYPES_FOR_BUSY_RATE = [
        EStaffContractType::FIXED_TERM_CONTRACT,
        EStaffContractType::INDEFINITE_TERM_CONTRACT,
        EStaffContractType::PROBATIONARY_CONTRACT,
    ];
}
