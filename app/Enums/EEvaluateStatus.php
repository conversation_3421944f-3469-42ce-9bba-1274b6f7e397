<?php

namespace App\Enums;

class EEvaluateStatus
{
    public const GOOD = 1;

    public const  WARNING = 2;

    public const  SERIOUS = 3;

    public const  NA = 4;

    public static function getAll()
    {
        return [
            self::GOOD,
            self::WARNING,
            self::SERIOUS,
            self::NA,
        ];
    }

    public static function getMasterData()
    {
        return [
            self::GOOD => 'good',
            self::WARNING => 'warning',
            self::SERIOUS => 'serious',
            self::NA => 'na',
        ];
    }
}
