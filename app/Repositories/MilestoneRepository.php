<?php

namespace App\Repositories;

use App\Interfaces\MilestoneRepositoryInterface;
use App\Models\Milestone;

class MilestoneRepository extends BaseRepository implements MilestoneRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Milestone::class;
    }

    public function store($args)
    {
        return $this->model->create($args);
    }

    public function update($id, $args)
    {
        $data = $this->model->where('id', $id)->first();
        if (! $data) {
            return null;
        }

        $data->update($args);

        return $data;
    }

    public function destroy($id)
    {
        return $this->model->find($id)->delete();
    }

    public function getLastItem($stageId)
    {
        return  $this->model->where('stage_id', $stageId)->orderBy('id', 'DESC')->latest('id')->first();
    }

    public function getCollectionId($stageId)
    {
        return $this->model->where('stage_id', $stageId)->orderBy('id', 'DESC')->pluck('id')->toArray();
    }

    public function getDetail($id)
    {
        return $this->model
            ->where('id', $id)
            ->with('stages')
            ->first();
    }
}
