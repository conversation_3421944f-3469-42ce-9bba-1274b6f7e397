<?php

namespace App\Repositories;

use App\Interfaces\PmReportRepositoryInterface;
use App\Models\PmReport;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class PmReportRepository extends BaseRepository implements PmReportRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return PmReport::class;
    }

    public function store($args)
    {
        return $this->model->create($args);
    }

    public function getListPmReport($args, $projectId)
    {
        $query = $this->model->select('*')->where('project_id', $projectId)->with('projects', 'pcv');

        return $query->orderByDesc('date')->paginate($args['limit'] ?? Config::get('define.projectPagination'));
    }

    public function getAllPmReports($args)
    {
        $query = $this->model->select('*')->with('projects', 'pcv');
        if (isset($args['project_ids'])) {
            $query->whereIn('project_id', $args['project_ids']);
        }

        if (isset($args['date'])) {
            $date = Carbon::parse($args['date']);
            $query->whereBetween(
                'date',
                [
                    $date->startOfWeek()->toDateString(),
                    $date->endOfWeek()->toDateString(),
                ]
            );
        }

        $args['division_ids'] = $args['division_ids'] ?? @$args['division_id'];
        if (!empty($args['division_ids'])) {
            $query->whereHas('projects', function ($q) use ($args) {
                 $q->whereIn('projects.division_id', (array) $args['division_ids']);
            });
        }

        $pmReports = $query->orderByDesc('updated_at')->paginate($args['limit'] ?? Config::get('define.projectPagination'));
        $tmpPmReports = $pmReports->getCollection();
        $tmpPmReports->map(function ($report) {
            $pcv = $report->pcv->first(fn ($pcv) => $pcv->project_id === $report->project_id);
            unset($report->pcv);

            return $report->pcv = $pcv;
        });
        $pmReports->setCollection($tmpPmReports);

        return $pmReports;
    }

    public function countPmReportedByWeek($date, $projectId)
    {
        $date = Carbon::parse($date);

        return $this->model
            ->where('project_id', $projectId)
            ->whereBetween(
                'date',
                [
                    $date->startOfWeek()->toDateString(),
                    $date->endOfWeek()->toDateString(),
                ]
            )
            ->count();
    }
}
