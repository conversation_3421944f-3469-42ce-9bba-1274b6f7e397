<?php

namespace App\Repositories;

use App\Models\Task;

class TaskRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Task::class;
    }

    public function getListTask($args)
    {
        $tasks = $this->model->select($args['fields'] ?? '*')
            ->whereNotNull('user_id')
            ->when(@$args['project_ids'], function ($query) use ($args) {
                $query->whereIn('project_id', $args['project_ids']);
            })
            ->when(@$args['month'], function ($query) use ($args) {
                $query->where('start_date', 'like', $args['month'] . '%');
            });

        return $tasks->get();
    }

    public function deleteByIssueIds($array)
    {
        return $this->model->whereIn('issue_id', $array)->delete();
    }
}
