<?php

namespace App\Repositories;

use App\Models\UserUnpaidLeave;
use Illuminate\Support\Facades\Config;

class UserUnpaidLeaveRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return UserUnpaidLeave::class;
    }

    public function getList($args = [])
    {
        $query = $this->model
            ->when(@$args['user_id'], function ($query) use ($args) {
                $query->whereIn('user_id', (array)$args['user_id']);
            })
            ->when(@$args['start_date'], function ($query) use ($args) {
                $query->whereDate('start_date', '<=', $args['start_date']);
            })
            ->when(@$args['end_date'], function ($query) use ($args) {
                $query->whereDate('end_date', '>=', $args['end_date']);
            })
            ->when(@$args['user_id'], function ($query) use ($args) {
                $query->whereIn('user_id', (array)$args['user_id']);
            })
            ->orderByDesc('id');

        if (@$args['page']) {
            return $query->paginate(
                $args['limit'] ?? Config::get('define.per_page_default'),
                ['*'],
                'page',
                $args['page'] ?? Config::get('define.page_default')
            );
        }

        return $query->get();
    }

    public function get($args)
    {
        $query = $this->model
            ->whereBetween('start_date', [$args['from'], $args['to']])
            ->orWhereBetween('end_date', [$args['from'], $args['to']])
            ->orWhere(function ($q) use ($args) {
                $q->whereDate('start_date', '<', $args['from'])
                    ->whereDate('end_date', '>', $args['to']);
            });

        return $query->get();
    }

    public function isExistUnpaidLeave($userId, $startDate, $endDate)
    {
        return $this->model
            ->where('user_id', $userId)
            ->where(function ($query) use ($startDate, $endDate) {
                $query->orWhere(function ($q) use ($startDate, $endDate) {
                    $q->whereDate('start_date', '>=', $startDate);
                    $q->whereDate('end_date', '<=', $endDate);
                });
                $query->orWhere(function ($query) use ($startDate, $endDate) {
                    $query->where('start_date', '<=', $startDate);
                    $query->where('end_date', '>=', $startDate);
                });
                $query->orWhere(function ($query) use ($startDate, $endDate) {
                    $query->where('start_date', '<=', $endDate);
                    $query->where('end_date', '>=', $endDate);
                });
            })
            ->exists();
    }
}
