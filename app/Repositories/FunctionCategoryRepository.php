<?php

namespace App\Repositories;

use App\Enums\ECommon;
use App\Interfaces\FunctionCategoryRepositoryInterface;
use App\Models\FunctionCategory;

class FunctionCategoryRepository extends BaseRepository implements FunctionCategoryRepositoryInterface
{
    protected $columns = [
        'id',
        'project_id',
        'name',
        'created_at',
    ];

    protected $relations = [];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * {@inheritDoc}
     */
    public function model()
    {
        return FunctionCategory::class;
    }

    public function getByProject($projectIds)
    {
        $condition = [
            'project_id' => $projectIds,
        ];

        return $this->list($condition, $this->columns, ECommon::BUILDER_TYPE_ELOQUENT, $this->relations);
    }
}
