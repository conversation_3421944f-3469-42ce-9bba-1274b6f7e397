<?php

namespace App\Repositories;

use App\Enums\ECommon;
use App\Interfaces\RepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

abstract class BaseRepository implements RepositoryInterface
{
    /**
     * @var \Main\Eloquent\Model
     */
    protected $model;

    private static $defaultValues;

    /**
     * AppRepository constructor.
     */
    public function __construct()
    {
        $this->makeModel();
    }

    /**
     * get model
     *
     * @return mixed
     */
    abstract public function model();

    /**
     * Set model
     */
    public function makeModel()
    {
        $model = (string) $this->model();
        if (! app()->make($model)) {
            app()->singleton($model, function () use ($model) {
                return new $model;
            });
        }
        $this->model = app()->make($model);
    }

    /**
     * If missing any method for repository
     * it's will be call with default
     *
     * @param  string  $method
     * @param  array  $args
     * @return QueryBuilder
     */
    public function __call($method, $args)
    {
        return $this->model->$method(...$args);
    }

    /**
     * Get All
     *
     * @param  array  $columns
     * @return mixed
     */
    public function all($columns = ['*'])
    {
        return $this->model->all($columns);
    }

    /**
     * Get list of models.
     *
     * @param  array  $condition
     * @param  array|string  $columns
     * @param  int  $builderType
     * @param  array  $relations
     * @param  bool  $withTrashed
     * @return EloquentCollection|Collection
     */
    public function list(
        array $condition = [],
        array|string $columns = ['*'],
        int $builderType = ECommon::BUILDER_TYPE_ELOQUENT,
        array $relations = [],
        bool $withTrashed = false
    ) {
        $query = $this->getBuilder($builderType, $relations, $withTrashed)->select($columns);

        return $this->addQuery($query, $condition)->get();
    }

    /**
     * Get list of models with pagination.
     *
     * @param  array  $condition
     * @param  array|string  $columns
     * @param  int  $builderType
     * @param  array  $relations
     * @param  bool  $withTrashed
     * @return LengthAwarePaginator
     */
    public function listPagination(
        array $condition = [],
        array|string $columns = ['*'],
        int $builderType = ECommon::BUILDER_TYPE_ELOQUENT,
        array $relations = [],
        bool $withTrashed = false
    ) {
        $query = $this->getBuilder($builderType, $relations, $withTrashed)->select($columns);

        return $this->addQuery($query, $condition)->paginate($condition['per_page'], $columns, 'page', $condition['page']);
    }

    /**
     * Lists
     *
     * @param $column
     * @param  null  $key
     * @return mixed
     */
    public function lists($column, $key = null)
    {
        return $this->model->pluck($column, $key);
    }

    /**
     * Paginate
     *
     * @param  null  $limit
     * @param  array  $columns
     * @return mixed
     */
    public function paginate($limit = null, $columns = ['*'])
    {
        $limit = is_null($limit) ? config('settings.paginate') : $limit;

        return $this->model->paginate($limit, $columns);
    }

    /**
     * Find
     *
     * @param $id
     * @param  array  $column
     * @return mixed
     */
    public function find($id, $column = ['*'])
    {
        return $this->model->find($id, $column);
    }

    public function findOrFail($id, $column = ['*'])
    {
        return $this->model->findOrFail($id, $column);
    }

    public function firstWhere(array $where, $columns = ['*'])
    {
        return $this->model->where($where)->get($columns)->first();
    }

    /**
     * Find
     *
     * @param $id
     * @param  array  $column
     * @return mixed
     */
    public function first()
    {
        return $this->model->first();
    }

    /**
     * Where
     *
     * @param $condition
     * @param  null  $operator
     * @param  null  $value
     * @return mixed
     */
    public function where($condition, $operator = null, $value = null)
    {
        return $this->model->where($condition, $operator, $value);
    }

    /**
     * Or where
     *
     * @param $column
     * @param  null  $operator
     * @param  null  $value
     * @return mixed
     */
    public function orWhere($column, $operator = null, $value = null)
    {
        return $this->model->orWhere($column, $operator, $value);
    }

    /**
     * Create
     *
     * @param $input
     * @return mixed
     */
    public function firstOrCreate($input = [])
    {
        return $this->model->firstOrCreate($input);
    }

    /**
     * Insert
     *
     * @param $input
     * @return mixed
     */
    public function insert($input)
    {
        return $this->model->insert($input);
    }

    /**
     * Insert
     *
     * @param $input
     * @return mixed
     */
    public function create(array $input)
    {
        return $this->model->create($input);
    }

    /**
     * @param $id
     * @param $input
     * @return mixed
     */
    public function update($id, $input)
    {
        $result = $this->model->find($id);
        if ($result) {
            $result->update($input);

            return $result;
        }

        return false;
    }

    /**
     * update Or Create model
     *
     * @param $arrClause
     * @param $arrUpdate
     * @return mixed
     */
    public function updateOrCreate($arrClause, $arrUpdate)
    {
        return $this->model->updateOrCreate($arrClause, $arrUpdate);
    }

    /**
     * Multi Update
     *
     * @param $column
     * @param $value
     * @param $input
     * @return mixed
     */
    public function multiUpdate($id, $array_of_ids, $fields)
    {
        // $array_of_ids = is_array($array_of_ids) ? $array_of_ids : [$array_of_ids];
        return $this->model->whereIn($id, $array_of_ids)->update($fields);
    }

    /**
     * Delete
     *
     * @param $ids
     * @return mixed
     */
    public function delete($ids)
    {
        if (empty($ids)) {
            return true;
        }
        $ids = is_array($ids) ? $ids : [$ids];

        return $this->model->destroy($ids);
    }

    /**
     * Soft Delete
     *
     * @param $ids
     * @return mixed
     */
    public function softDelete($name, $ids, $input)
    {
        if (empty($ids)) {
            return true;
        }
        $ids = is_array($ids) ? $ids : [$ids];

        $this->multiUpdate($name, $ids, $input);

        //return $this->model->destroy($ids);
    }

    // /**
    //  * @param $relations
    //  * @return mixed
    //  */
    // public function with($relations)
    // {
    //     return $this->model->with($relations);
    // }

    public function with($relations)
    {
        return static::query()->with(
            is_string($relations) ? func_get_args() : $relations
        );
    }

    /**
     * Order by
     *
     * @param $column
     * @param  string  $direction
     * @return mixed
     */
    public function orderBy($column, $direction = 'asc')
    {
        return $this->model->orderBy($column, $direction);
    }

    /**
     * Count has condition
     *
     * @param  array  $where
     * @param  array  $columns
     * @return mixed
     */
    public function countWhere(array $where, array $columns = ['*'])
    {
        return $this->model->where($where)->count();
    }

    /**
     * With count
     *
     * @param $relation
     * @return mixed
     */
    public function withCount($relation)
    {
        return $this->model->withCount($relation);
    }

    /**
     * Sum with condition
     *
     * @param $where
     * @param $field
     * @return mixed
     */
    public function sumWhere($where, $field)
    {
        return $this->model->where($where)->sum($field);
    }

    /**
     * Select
     *
     * @param  array  $columns
     * @return mixed
     */
    public function select($columns = ['*'])
    {
        return $this->model->select($columns);
    }

    /**
     * Load relation with closure
     *
     * @param $relation
     * @param $closure
     * @return mixed
     */
    public function whereHas($relation, $closure)
    {
        return $this->model->whereHas($relation, $closure);
    }

    /**
     * where in
     *
     * @param $column
     * @param  array  $values
     * @return $this
     */
    public function whereIn($column, array $values)
    {
        $values = is_array($values) ? $values : [$values];

        return $this->model->whereIn($column, $values);
    }

    /**
     * where not in
     *
     * @param $column
     * @param  array  $values
     * @return $this
     */
    public function whereNotIn($column, array $values)
    {
        $values = is_array($values) ? $values : [$values];

        return $this->model->whereNotIn($column, $values);
    }

    /**
     * @param  string  $relation
     * @return $this
     */
    public function has($relation)
    {
        return $this->model->with($relation);
    }

    /**
     * Join with other take
     *
     * @param  string  $table
     * @param  string  $columnTableA
     * @param  string  $condition
     * @param  string  $columnTableB
     * @return $this
     */
    public function join($table, $columnTableA = null, $condition = null, $columnTableB = null)
    {
        return $this->model->join($table, $columnTableA, $condition, $columnTableB);
    }

    /**
     * When function check condition to execute query
     *
     * @param  string  $condition
     * @param  Closure  $callback
     * @param  Closure  $default
     * @return $this
     */
    public function when($condition, $callback, $default = null)
    {
        return $this->model->when($condition, $callback, $default);
    }

    /**
     * Update
     *
     * @param $id
     * @param  array  $attributes
     * @return bool|mixed
     */
    public function filter($args)
    {
        return $oQuery = $this->_model->filter($args);
    }

    //get by option
    public function getByOption($options = [], $queryBuilder = null)
    {
        if (! empty($queryBuilder)) {
            $queryBuilder = $this->model->select('*');
        }

        if (! empty($options['orderBy'])) {
            $queryBuilder->orderBy($options['orderBy']['column'], $options['orderBy']['type']);
        } else {
            $queryBuilder->orderBy('id', 'desc');
        }

        if (! empty($options['exists'])) {
            return $queryBuilder->exists();
        }
        if (! empty($options['first'])) {
            return $this->model->first();
        }
        if (! empty($options['pageSize'])) {
            return $queryBuilder->paginate($options['pageSize']);
        }
        if (! empty($options['firstOrFail'])) {
            return $queryBuilder->firstOrFail();
        }

        return $queryBuilder->get();
    }

    //
    public function replaceFields($insertFields, $whereFields, $fieldNames = [])
    {
        //Delete old Data
        $delete_query = $this->model->where($whereFields);
        foreach ($fieldNames as $key => $val) {
            if (! isset($val)) {
                continue;
            }
            $delete_query->whereIn($key, $val);
        }
        $delete_query->delete();

        //Insert new Data
        $this->model->insert($insertFields);
    }

    public function updateWhere($where, $fields)
    {
        return $this->model
            ->where($where)
            ->update($fields);
    }

    public function deleteWhere($where)
    {
        return $this->model
            ->where($where)
            ->delete();
    }

    /**
     * Querying the model.
     *
     * @param $query
     * @param  array  $condition
     * @return EloquentBuilder|QueryBuilder
     */
    protected function addQuery($query, array $condition)
    {
        return $this->whereClause($query, $condition);
    }

    /**
     * Add "where" clause to the query.
     *
     * @param $query
     * @param  array  $condition
     * @return EloquentBuilder|QueryBuilder
     */
    protected function whereClause($query, array $condition)
    {
        foreach ($condition as $column => $value) {
            if (is_string($column)) {
                if (in_array($column, $this->model->getFillable())) {
                    $method = ! is_array($value) ? 'where' : 'whereIn';
                    $query->{$method}($column, $value);
                }
            } elseif (is_numeric($column) && is_array($value)) {
                $query->where([$value]);
            }
        }

        return $query;
    }

    /**
     * Get Builder.
     *
     * @param $builderType
     * @param  array  $relations
     * @param  bool  $withTrashed
     * @return EloquentBuilder|QueryBuilder
     */
    protected function getBuilder($builderType, array $relations = [], bool $withTrashed = false)
    {
        if ($builderType == ECommon::BUILDER_TYPE_QUERY) {
            $builder = DB::connection($this->model->getConnectionName())->table($this->model->getTable());
            if (! $withTrashed && $this->checkSoftDeletesUsageModel()) {
                $builder->whereNull($this->model->getTable().'.'.$this->model->getDeletedAtColumn());
            }
        } else {
            $builder = $this->model->newQuery();
            if (! empty($relations)) {
                $validRelations = array_filter($relations, function ($relation) {
                    return method_exists($this->model, $relation);
                });
                $builder->with($validRelations);
            }
            if ($withTrashed && $this->checkSoftDeletesUsageModel()) {
                $builder->withTrashed();
            }
        }

        return $builder;
    }
}
