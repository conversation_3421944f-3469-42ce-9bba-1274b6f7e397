<?php

namespace App\Repositories;

use App\Interfaces\PermissionRoleRepositoryInterface;
use App\Models\PermissionRole;

class PermissionRoleRepository extends BaseRepository implements PermissionRoleRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return PermissionRole::class;
    }

    public function getList($args = [])
    {
        return $this->model
            ->selectRaw('permission_role.*, permissions.name as permission_name')
            ->join('permissions', 'permissions.id', '=', 'permission_role.permission_id')
            ->when(isset($args['is_position']), function ($query) {
                $query->whereNotNull('position_id');
            })
            ->when(isset($args['position_id']), function ($query) use ($args) {
                $query->where('position_id', $args['position_id']);
            })
            ->get();
    }
}
