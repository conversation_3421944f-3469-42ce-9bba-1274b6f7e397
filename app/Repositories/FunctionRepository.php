<?php

namespace App\Repositories;

use App\Enums\ECommon;
use App\Interfaces\FunctionRepositoryInterface;
use App\Models\ProjectFunction;
use Illuminate\Database\Eloquent\Builder;

class FunctionRepository extends BaseRepository implements FunctionRepositoryInterface
{
    protected $columns = [
        'id',
        'project_id',
        'function_category_id',
        'sprint_id',
        'name',
        'story_point',
        'estimate',
        'work_completed',
        'note',
        'created_at',
    ];

    protected $relations = ['functionCategory', 'sprint'];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * {@inheritDoc}
     */
    public function model()
    {
        return ProjectFunction::class;
    }

    /**
     * Get functions by project ID
     *
     * @param int|array $projectIds
     * @return mixed
     */
    public function getByProject($projectIds)
    {
        $condition = [
            'project_id' => $projectIds,
        ];

        return $this->list($condition, $this->columns, ECommon::BUILDER_TYPE_ELOQUENT, $this->relations);
    }

    /**
     * Add query conditions for functions
     * 
     * @param Builder $query
     * @param array $condition
     * @return Builder
     */
    protected function whereClause($query, array $condition)
    {
        // Call parent's whereClause method first
        $query = parent::whereClause($query, $condition);

        if (isset($condition['search']) && $condition['search']) {
            $query->where('name', 'like', '%' . $condition['search'] . '%');
        }

        if (isset($condition['order_by']) && isset($condition['order_type'])) {
            $query->orderBy($condition['order_by'], $condition['order_type']);
        }

        return $query;
    }
}
