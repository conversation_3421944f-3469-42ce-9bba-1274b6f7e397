<?php

namespace App\Repositories;

use App\Interfaces\ProjectStakeholderRepositoryInterface;
use App\Models\ProjectStakeholder;

class ProjectStakeholderRepository extends BaseRepository implements ProjectStakeholderRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return ProjectStakeholder::class;
    }

    public function getIdsByProject($projectIds)
    {
        return $this->select('*')->whereIn('project_id', $projectIds)->get();
    }

    public function addStakeholder($projectId, $stakeholderIds = [])
    {
        $this->model->where([
            ['project_id', '=', $projectId],
        ])->delete();
        foreach ($stakeholderIds as $stakeholder_idd) {
            $this->model->firstOrCreate([
                'stakeholder_id' => $stakeholder_idd,
                'project_id' => $projectId,
            ])->toArray();
        }
    }
}
