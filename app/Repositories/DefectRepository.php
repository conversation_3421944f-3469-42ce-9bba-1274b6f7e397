<?php

namespace App\Repositories;

use App\Enums\ECommon;
use App\Interfaces\DefectRepositoryInterface;
use App\Models\Defect;

class DefectRepository extends BaseRepository implements DefectRepositoryInterface
{
    protected $columns = [
        'id',
        'project_id',
        'sprint_id',
        'type',
        'severity',
        'total',
        'created_at',
        'updated_at',
    ];

    protected $relations = [];


    public function __construct()
    {
        parent::__construct();
    }

    /**
     * {@inheritDoc}
     */
    public function model()
    {
        return Defect::class;
    }

    public function getByProject($projectIds)
    {
        $condition = [
            'project_id' => $projectIds,
        ];

        return $this->list($condition, $this->columns, ECommon::BUILDER_TYPE_ELOQUENT, $this->relations);
    }
}
