<?php

namespace App\Repositories;

use App\Enums\EProjectStatus;
use App\Enums\EProjectType;
use App\Enums\ERole;
use App\Interfaces\ProjectUserRoleRepositoryInterface;
use App\Models\ProjectRoleUser;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;

class ProjectUserRoleRepository extends BaseRepository implements ProjectUserRoleRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return ProjectRoleUser::class;
    }

    public function addMember($args = arr)
    {
        foreach ($args['user_ids'] as $user_id) {
            $this->model->firstOrCreate([
                'user_id' => $user_id,
                'project_id' => $args['project_id'],
                'role_id' => $args['role_id'],
                'position_in_project' => $args['position_in_project'] ?? null
            ]);
        }

        $pivot = $this->model
            ->where('project_id', $args['project_id'])
            ->get()
            ->toArray();

        return $pivot;
    }

    public function getUsersByRoles($project_id, $role_ids)
    {
        return $this->where('project_id', $project_id)->whereIn('role_id', $role_ids);
    }

    public function addMemberWhenAddAllocation($args, $projectId)
    {
        foreach ($args['user_ids'] as $key => $user_id) {
            $this->model->firstOrCreate([
                'user_id' => $user_id,
                'project_id' => $projectId,
                'role_id' => $args['role_ids'][$key],
            ]);
        }
    }

    public function getPivotData($id, $args)
    {
        $isGetDl = $args['get_dl'] ?? 1;

        if (isset($args['pagination']) && ($args['pagination'] == false || $args['pagination'] == 'false'))
            return $this->where('project_id', $id)->get();

        return $this->where('project_id', $id)
            ->when($isGetDl == 0, function ($query) {
                $query->whereNotIn('role_id', ERole::GROUP_DL);
            })
            ->paginate($args['limit'] ?? Config::get('define.customerPagination'));
    }

    public function getIdsByProject($projectIds, $type)
    {
        return $this->select('*')
            ->whereIn('project_id', $projectIds)
            ->where('role_id', $type)
            ->get();
    }

    public function removeMember($id)
    {
        return $this->model->destroy($id);
    }

    public function destroyWithRole($projectId, $roleId)
    {
        return $this->model->where([
            ['project_id', $projectId],
            ['role_id', $roleId],
        ])->delete();
    }

    public function destroyWithRoleDL($userId)
    {
        return $this->model
            ->where('user_id', $userId)
            ->whereIn('role_id', ERole::GROUP_DL)
            ->delete();
    }

    public function checkExistUser($userIds, $projectId)
    {
        return $this->model
            ->where('project_id', $projectId)
            ->whereIn('user_id', $userIds)
            ->get();
    }

    public function getProjectUserRole($args)
    {
        $userId = $args['user_ids'] ?? [];
        $projectId = $args['project_id'] ?? null;

        return $this->model
            ->select(
                'project_id',
                'project_role_user.role_id',
                'user_id',
                'permissions.name as permission_name'
            )
            ->join('permission_role', 'permission_role.role_id', '=', 'project_role_user.role_id')
            ->join('permissions', 'permissions.id', '=', 'permission_role.permission_id')
            ->when($userId, function ($query) use ($userId) {
                $query->whereIn('user_id', Arr::wrap($userId));
            })
            ->when($projectId, function ($query) use ($projectId) {
                $query->where('project_id', $projectId);
            })
            ->get();
    }

    public function userBelongToProject($userIds)
    {
        return $this->with('project:id,name')->whereIn('user_id', $userIds)->get();
    }

    public function getIdUsersProject($projectId)
    {
        return $this->model
            ->selectRaw('DISTINCT user_id')
            ->where('project_id', $projectId)
            ->get();
    }

    public function getProjectsOfUser($userId, $args)
    {
        $keyWord = $args['key_word'] ?? null;

        $query = $this->model
            ->selectRaw('DISTINCT projects.id as id, projects.name as name')
            ->join('projects', 'projects.id', '=', 'project_role_user.project_id')
            ->whereIn('user_id', (array)$userId)
            ->whereNull('projects.deleted_at')
            ->when(@$args['status'], fn($query) => $query->whereIn('status', $args['status']))
            ->when($keyWord, function ($q) use ($keyWord) {
                $q->where(function ($q) use ($keyWord) {
                    $q->where('name', 'like', '%' . $keyWord . '%')
                        ->orWhere('code', 'like', '%' . $keyWord . '%');
                });
            });

        if (@$args['page']) {
            return $query->paginate($args['limit'] ?? Config::get('define.per_page_default'));
        }

        return $query->get();
    }

    public function getUsersInProject($projectIds)
    {
        return $this->model
            ->whereIn('project_id', $projectIds)
            ->whereNotIn('role_id', [ERole::DL, ERole::SUB_DL, ERole::AL])
            ->get();
    }

    public function getMembersByRole($roleId)
    {
        return $this->select('*')->where('role_id', $roleId)->get();
    }
}
