<?php

namespace App\Repositories;

use App\Interfaces\ProjectRepositoryInterface;
use App\Models\Project;
use App\Traits\Sort;
use Carbon\Carbon;
use App\Enums\ERole;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class ProjectRepository extends BaseRepository implements ProjectRepositoryInterface
{
    use Sort;

    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Project::class;
    }

    public function store($args = arr): Project
    {
        return $this->model->create($args);
    }

    public function getProjects($args = null)
    {
        return $this->model
            ->select($args['fields'] ?? '*')
            ->when(@$args['division_ids'], function ($query) use ($args) {
                $query->whereIn('division_id', Arr::wrap($args['division_ids']));
            })
            ->get();
    }

    public function getListProject($args = null)
    {
        $query = $this->model->select($args['fields'] ?? '*')
            ->with(
                'customers:id,hubspot_id,name,original_name',
                'allocations',
                'pms',
                'pqas',
                'sellers',
                'stages:id,project_id,start_date,end_date,replan_start_date,replan_end_date'
            );
        if (isset($args['relations'])) $query->with($args['relations']);
        $query = $this->addConditions($query, $args);
        $query = isset($args['column']) && isset($args['dir']) ?
            $query->orderBy($args['column'], $args['dir']) :
            $query->orderByDesc('created_at');

        if (!empty($args['not_status'])) {
            $query->whereNotIn('status', $args['not_status']);
        }
        if (isset($args['pagination']) && ($args['pagination'] == false || $args['pagination'] == 'false')) {
            return $query->get();
        }

        return $query->paginate($args['limit'] ?? Config::get('define.projectPagination'));
    }

    public function addConditions($query, $args)
    {
        if (!empty($args['status'])) {
            $query->whereIn('status', $args['status']);
        }
        if (isset($args['project_type'])) {
            $query->whereIn('project_type', $args['project_type']);
        }
        if (isset($args['name'])) {
            $name = $args['name'];
            $query->where(function ($q) use ($name) {
                $q->orWhere('name', 'like', '%' . $name . '%')
                    ->orWhere('code', 'like', '%' . $name . '%');
            });
        }
        if (isset($args['rank'])) {
            $query->whereIn('rank', $args['rank']);
        }
        if (isset($args['ee'])) {
            $query->whereIn('ee', $args['ee']);
        }
        if (isset($args['pm_ids'])) {
            $query->whereHas('pms', function ($q) use ($args) {
                $q->whereIn('user_id', $args['pm_ids']);
            });
        }
        if (isset($args['customer_ids'])) {
            $query->whereHas('customers', function ($q) use ($args) {
                $q->whereIn('customer_id', $args['customer_ids']);
            });
        }
        if (isset($args['contract_type'])) {
            $query->whereIn('contract_type', $args['contract_type']);
        }
        if (isset($args['start_date'])) {
            $query->whereDate('start_date', '>=', Carbon::parse($args['start_date'])->format('Y-m-d'));
        }
        if (isset($args['end_date'])) {
            $query->whereDate('end_date', '<=', Carbon::parse($args['end_date'])->format('Y-m-d'));
        }

        if (isset($args['billable_from'])) {
            $query->where('billable', '>=', $args['billable_from']);
        }

        if (isset($args['billable_to'])) {
            $query->where('billable', '<=', $args['billable_to']);
        }

        /**
         * project_ids là danh sách dự án đc add là member
         */
        if (isset($args['project_ids']) && isset($args['division_ids']) && (bool) (int) $args['single_division'] == true) {
            $query->whereIn('id', $args['project_ids']);
            $query->whereIn('division_id', $args['division_ids']);
        } elseif ((isset($args['admin_id']) || isset($args['pqa_id'])) && (bool) (int) $args['single_division'] == true) {
            $query->whereIn('division_id', $args['division_ids']);
        } elseif (isset($args['project_ids']) && isset($args['division_ids'])) {
            $query->where(function ($q) use ($args) {
                $q->orWhereIn('id', $args['project_ids']);
                $q->orWhereIn('division_id', $args['division_ids']);
            });
        } elseif (isset($args['project_ids']) && !isset($args['division_ids'])) {
            $query->whereIn('id', $args['project_ids']);
        }
        return $query;
    }

    public function update($id, $args = arr)
    {
        $project = $this->model->find($id);
        if (!$project) {
            return null;
        }

        $project->update($args);

        return $project;
    }

    public function checkDivisionProject($division_id, $project_id)
    {
        return $this->model->where('id', $project_id)
            ->where('division_id', $division_id)
            ->first();
    }

    public function getProjectsByKeyword($keyword)
    {
        $query = $this->model->select(
            DB::raw('distinct(projects.id)'),
        )
            ->leftJoin('customer_project', 'projects.id', '=', 'customer_project.project_id')
            ->leftJoin('customers', 'customers.id', '=', 'customer_project.customer_id')
            ->whereNull('projects.deleted_at')
            ->where(function ($q) use ($keyword) {
                $q->where('projects.name', 'like', '%' . $keyword . '%')
                    ->orWhere('customers.name', 'like', '%' . $keyword . '%');
            });
        return $query->get();
    }

    public function getProjectsByCustomer($customerId)
    {
        $query = $this->model->select(
            DB::raw('distinct(projects.id)'),
        )
            ->join('customer_project', 'projects.id', '=', 'customer_project.project_id')
            ->join('customers', 'customers.id', '=', 'customer_project.customer_id')
            ->whereNull('projects.deleted_at')
            ->where('customers.id', $customerId);
        return $query->get();
    }

    public function getRawProjects($args)
    {
        $fields = $args['fields'] ?? '*';
        $query = $this->addRawProjectConditions($this->model->select($fields), $args);
        return $query->get();
    }

    private function addRawProjectConditions($query, $args)
    {
        if (isset($args['project_ids'])) {
            $query->whereIn('id', $args['project_ids']);
        }
        if (isset($args['legal'])) {
            $query->where('legal', $args['legal']);
        }
        if (isset($args['contract_type'])) {
            $query->where('contract_type', $args['contract_type']);
        }
        if (isset($args['relations'])) $query->with($args['relations']);

        return $query;
    }

    public function getWorklogReport($args)
    {
        $month = $args['month'];
        $query = $this->model->select(
            'projects.id',
            'projects.name',
            'daily_reports.user_id',
            'daily_reports.work_date',
            DB::raw('SUM(daily_reports.actual_time) AS actual_time')
        )
            ->leftJoin('project_role_user', 'projects.id', '=', 'project_role_user.project_id')
            ->leftJoin('daily_reports', 'projects.id', '=', 'daily_reports.project_id')
            ->whereRaw('DATE_FORMAT(projects.start_date, "%Y-%m") <=' . "'" . $month . "'")
            ->where(function ($q) use ($month) {
                $q->whereRaw('DATE_FORMAT(projects.end_date, "%Y-%m") >=' . "'" . $month . "'");
                $q->orWhereNull('projects.end_date');
            })
            ->where('project_role_user.user_id', $args['user_id'])
            ->where('project_role_user.role_id', ERole::PM)
            ->whereRaw('DATE_FORMAT(daily_reports.work_date, "%Y-%m") =' . "'" . $month . "'")
            ->groupBy('projects.id', 'daily_reports.user_id', 'daily_reports.work_date')
            ->orderBy('projects.created_at', 'desc')
            ->orderBy('daily_reports.work_date', 'asc')
            ->get();

        return $query;
    }

    public function getProjectByCode($args)
    {
        return $this->model->select($args['fields'] ?? '*')
            ->when(@$args['code'], function ($query) use ($args) {
                $query->whereIn('code', $args['code']);
            })
            ->when(@$args['id'], function ($query) use ($args) {
                $query->where('id', $args['id']);
            })
            ->get();
    }
}
