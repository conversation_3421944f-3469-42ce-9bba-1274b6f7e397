<?php

namespace App\Repositories;

use App\Interfaces\RequirementChangeRepositoryInterface;
use App\Models\RequirementChange;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

class RequirementChangeRepository extends BaseRepository implements RequirementChangeRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return RequirementChange::class;
    }

    public function store($args)
    {
        return $this->model->create($args);
    }

    public function update($id, $args)
    {
        $data = $this->model->where('id', $id)->first();
        if (! $data) {
            return null;
        }

        $data->update($args);

        return $data;
    }

    public function destroy($deleteIds)
    {
        return   $this->model->whereIn('id', $deleteIds)->delete();
    }

    public function getDetail($id)
    {
        return $this->model
            ->where('id', $id)
            ->with('projects', 'stages', 'file_evidences')
            ->first();
    }

    public function getList($args, $projectId)
    {
        return $this->model
            ->where('project_id', $projectId)
            ->with('projects:id,name', 'stages', 'file_evidences')
            ->when(isset($args['stage_id']), function ($query) use ($args) {
                return $query->where('stage_id', $args['stage_id']);
            })
            ->when(isset($args['request_by']), function ($query) use ($args) {
                return $query->where('request_by', $args['request_by']);
            })
            ->when(isset($args['status']), function ($query) use ($args) {
                return $query->where('status', $args['status']);
            })
            ->when(isset($args['type']), function ($query) use ($args) {
                return $query->where('type', $args['type']);
            })
            ->when(isset($args['start_date']), function ($query) use ($args) {
                return $query->whereDate('request_date', '>=', $args['start_date']);
            })
            ->when(isset($args['end_date']), function ($query) use ($args) {
                return $query->whereDate('request_date', '<=', $args['end_date']);
            })
            ->when(isset($args['keyword']), function ($query) use ($args) {
                return $query->where(function ($query) use ($args) {
                    if (Str::contains($args['keyword'], '%')) {
                        $operator = '=';
                    } else {
                        $operator = 'LIKE';
                        $args['keyword'] = "%{$args['keyword']}%";
                    }

                    return $query->where('title', $operator, $args['keyword'])
                        ->orWhere('id', $operator, $args['keyword']);
                });
            })
            ->when(isset($args['sort_request_date_desc']), function ($query) use ($args) {
                if ($args['sort_request_date_desc']) {
                    return $query->orderByDesc('request_date');
                } else {
                    return $query->orderByAsc('request_date');
                }
            })
            ->when(isset($args['sort_cost_desc']), function ($query) use ($args) {
                if ($args['sort_cost_desc']) {
                    return $query->orderByDesc('cost');
                } else {
                    return $query->orderByAsc('cost');
                }
            })
            ->orderBy('id', 'desc')
            ->paginate(
                $args['per_page'] ?? Config::get('define.change_request_pagination'),
                ['*'],
                'page',
                $args['page'] ?? Config::get('define.page_default')
            );
    }
}
