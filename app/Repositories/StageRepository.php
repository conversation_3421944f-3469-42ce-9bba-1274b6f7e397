<?php

namespace App\Repositories;

use App\Interfaces\StageRepositoryInterface;
use App\Models\Stage;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class StageRepository extends BaseRepository implements StageRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Stage::class;
    }

    public function store($args)
    {
        return $this->model->create($args);
    }

    public function update($id, $args)
    {
        $data = $this->model->where('id', $id)->first();
        if (! $data) {
            return null;
        }

        $data->update($args);

        return $data;
    }

    public function destroy($id)
    {
        return $this->model->find($id)->delete();
    }

    public function checkExistVersion($args, $id = null)
    {
        return $this->model
            ->where('project_id', $args['project_id'])
            ->where('version', $args['version'])
            ->where('type', $args['type'])
            ->when($id, function ($query) use ($id) {
                return $query->whereNotIn('id', Arr::wrap($id));
            })
            ->exists();
    }

    public function checkResourceAllocated($args, $id = null)
    {
        return $this->model
            ->where('project_id', $args['project_id'])
            ->where('version', $args['version'])
            ->where('type', $args['type'])
            ->when($id, function ($query) use ($id) {
                return $query->whereNotIn('id', Arr::wrap($id));
            })
            ->exists();
    }

    public function getDetail($stageId)
    {
        return $this->model
            ->where('id', $stageId)
            ->with('allocations', 'deliverables', 'milestones')
            ->first();
    }

    public function getList($projectId)
    {
        return $this->model
            ->where('project_id', $projectId)
            ->with('allocations', 'deliverables', 'milestones', 'project:id,name,start_date,end_date')
            ->get();
    }

    public function getAllocationByStageIds($projectId)
    {
        return $this->model
            ->whereIn('project_id', $projectId)
            ->get();
    }
}
