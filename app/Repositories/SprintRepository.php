<?php

namespace App\Repositories;

use App\Enums\ECommon;
use App\Interfaces\SprintRepositoryInterface;
use App\Models\Sprint;

class SprintRepository extends BaseRepository implements SprintRepositoryInterface
{
    protected $columns = [
        'id',
        'project_id',
        'name',
        'start_date',
        'end_date',
        'created_at',
        'updated_at',
    ];

    protected $relations = [];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * {@inheritDoc}
     */
    public function model()
    {
        return Sprint::class;
    }

    public function getByProject($projectIds)
    {
        $condition = [
            'project_id' => $projectIds,
        ];

        return $this->list($condition, $this->columns, ECommon::BUILDER_TYPE_ELOQUENT, $this->relations);
    }
}
