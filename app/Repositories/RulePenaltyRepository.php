<?php

namespace App\Repositories;

use App\Models\RulePenalty;

class RulePenaltyRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return RulePenalty::class;
    }

    public function deleteByColumn($column, $array)
    {
        return $this->model->whereIn($column, $array)->delete();
    }

    public function getPenalties($ruleId, $number)
    {
        return $this->model->select('penalties.id', 'penalties.name')
            ->join('penalties', 'penalties.id', '=', 'rule_penalties.penalty_id')
            ->where('rule_id', $ruleId)
            ->where('number', $number)
            ->get();
    }
}
