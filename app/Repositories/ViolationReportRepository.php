<?php

namespace App\Repositories;

use App\Models\ViolationReport;
use Illuminate\Support\Facades\Config;

class ViolationReportRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return ViolationReport::class;
    }

    public function getListViolationReport($args)
    {
        return $this->model->with(['project:id,name', 'rule:id,name'])
            ->when(!isset($args['is_admin']), function ($query) use ($args) {
                return $query->where('creator_id', $args['user_id'])
                    ->orWhere('handler_id', $args['user_id'])
                    ->orWhere('violator_id', $args['user_id']);
            })
            ->when(@$args['keyword'], function ($query) use ($args) {
                return $query->where('title', 'like', '%' . $args['keyword'] . '%');
            })
            ->when(@$args['team_ids'], function ($query) use ($args) {
                return $query->whereIn('team_id', $args['team_ids']);
            })
            ->when(@$args['project_id'], function ($query) use ($args) {
                return $query->where('project_id', $args['project_id']);
            })
            ->when(@$args['status'], function ($query) use ($args) {
                return $query->where('status', $args['status']);
            })
            ->when(@$args['violation_date'], function ($query) use ($args) {
                return $query->where('violation_date', $args['violation_date']);
            })
            ->orderByDesc('id')
            ->paginate($args['limit'] ?? Config::get('define.customerPagination'));
    }
}
