<?php

namespace App\Repositories;

use App\Interfaces\PcvReportRepositoryInterface;
use App\Models\PcvReport;

class PcvReportRepository extends BaseRepository implements PcvReportRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return PcvReport::class;
    }

    public function getPcvReports($projectId)
    {
        return $this->model->where('project_id', $projectId)->get();
    }

    public function store($args = arr)
    {
        return $this->model->create($args);
    }

    public function update($id, $args = arr)
    {
        $customer = $this->model->where('id', $id)->first();
        if (! $customer) {
            return null;
        }

        $customer->update($args);

        return $customer;
    }

    public function destroy($id)
    {
        return $this->model->where('id', $id)->delete();
    }
}
