<?php

namespace App\Repositories;

use App\Enums\EDailyReport;
use App\Models\DailyReport;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class DailyReportRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return DailyReport::class;
    }

    public function getList($args = [], $columns = ['*'])
    {
        $keyWord = $args['key_word'] ?? null;

        $query = $this->model->select($columns);

        if (isset($args['get_project']) && $args['get_project'] == 0) {
        } else $query = $query->with('project');

        $query = $query
            ->when(@$args['ids'], function ($query) use ($args) {
                $query->whereIn('id', $args['ids']);
            })
            ->when(@$args['user_id'], function ($query) use ($args) {
                $query->where('user_id', $args['user_id']);
            })
            ->when(@$args['user_ids'], function ($query) use ($args) {
                $query->whereIn('user_id', $args['user_ids']);
            })
            ->when(@$args['work_date'], function ($query) use ($args) {
                $query->whereDate('work_date', $args['work_date']);
            })
            ->when(isset($args['work_dates']), function ($query) use ($args) {
                $query->whereIn('work_date', $args['work_dates']);
            })
            ->when(@$args['from_date'], function ($query) use ($args) {
                $query->whereDate('work_date', '>=', $args['from_date']);
            })
            ->when(@$args['to_date'], function ($query) use ($args) {
                $query->whereDate('work_date', '<=', $args['to_date']);
            })
            ->when(@$args['project_id'], function ($query) use ($args) {
                $query->whereIn('project_id', (array)$args['project_id']);
            })
            ->when(@$args['status'], function ($query) use ($args) {
                $query->whereIn('status', (array)$args['status']);
            })
            ->when(@$args['month_in_year'], function ($query) use ($args) {
                $monthInYear = carbon($args['month_in_year']);

                $query->whereMonth('work_date', $monthInYear->month)
                    ->whereYear('work_date', $monthInYear->year);
            })
            ->when($keyWord, function ($query) use ($keyWord) {
                $query->where(function ($q) use ($keyWord) {
                    $q->where('title', 'like', '%' . $keyWord . '%')
                        ->orWhere('issue_key', 'like', '%' . $keyWord . '%');
                });
            });

        if (@$args['sort_date']) $query->orderBy('work_date', 'asc');
        $query->orderByDesc('id');

        if (@$args['page']) {
            return $query->paginate($args['limit'] ?? Config::get('define.per_page_default'));
        }

        return $query->get();
    }

    public function getTotalActualTimes($projectId, $fromDate, $toDate, $status)
    {
        return $this->model
            ->selectRaw('user_id, work_date, sum(actual_time) as total_time')
            ->where('project_id', $projectId)
            ->where('status', $status)
            ->when($fromDate, function ($query) use ($fromDate) {
                $query->whereDate('work_date', '>=', $fromDate);
            })
            ->when($toDate, function ($query) use ($toDate) {
                $query->whereDate('work_date', '<=', $toDate);
            })
            ->groupBy('user_id', 'work_date')
            ->get();
    }

    public function getDailyReportByUserIds($userIds, $date)
    {
        $query = $this->model->whereIn('user_id', $userIds)
            ->where('status', EDailyReport::SUCCESS_STATUS);

        if (isset($date['from']) && isset($date['to'])) {
            $query->whereDate('work_date', '>=', $date['from'])
                ->whereDate('work_date', '<=', $date['to']);
        }

        return $query->get();
    }

    public function getDailyReportByProjectIds($projectIds, $date)
    {
        $query = $this->model->whereIn('project_id', $projectIds)
            ->where('status', EDailyReport::SUCCESS_STATUS);

        if (isset($date['from']) && isset($date['to'])) {
            $query->whereDate('work_date', '>=', $date['from'])
                ->whereDate('work_date', '<=', $date['to']);
        }

        return $query->get();
    }

    public function getProjectTotalReportHour($filters = [])
    {
        return $this->model
            ->selectRaw('project_id, status, sum(actual_time) as hour')
            ->when(@$filters['month'], function ($query) use ($filters) {
                $month = Carbon::parse($filters['month'])->month;
                $query->whereMonth('work_date', $month);
            })
            ->when(@$filters['from'], function ($query) use ($filters) {
                $query->whereDate('work_date', '>=', $filters['from']);
            })
            ->when(@$filters['to'], function ($query) use ($filters) {
                $query->whereDate('work_date', '<=', $filters['to']);
            })
            ->when(@$filters['status'], function ($query) use ($filters) {
                $query->whereIn('status', (array)$filters['status']);
            })
            ->groupBy('project_id', 'status')
            ->get();
    }

    public function getUserWorkDateHour($userId, $dates)
    {
        return $this->model
            ->selectRaw('work_date, sum(actual_time) as hour')
            ->where('user_id', $userId)
            ->whereIn('status', EDailyReport::REPORTED_STATUSES)
            ->whereIn('work_date', (array)$dates)
            ->groupBy('work_date')
            ->get();
    }
}
