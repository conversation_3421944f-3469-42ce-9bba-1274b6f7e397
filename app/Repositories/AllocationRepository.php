<?php

namespace App\Repositories;

use App\Interfaces\AllocationRepositoryInterface;
use App\Models\Allocation;

class AllocationRepository extends BaseRepository implements AllocationRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Allocation::class;
    }

    public function store($args = arr)
    {
        return $this->model->create($args);
    }

    public function getAllocations($member_ids, $stageId)
    {
        $query = $this->model->select('*')
            ->with(
                'stage:id,start_date,end_date,project_id',
                'role',
                'stage.project:id,name'
            );
        if (!empty($member_ids)) {
            $allocations = $query->whereIn('user_id', $member_ids)->get();
        } else {
            $allocations = $query->where('stage_id', $stageId)->get();
        }

        return $allocations;
    }

    public function destroy($id)
    {
        return $this->model->where('id', $id)->delete();
    }

    public function getAllocationByUserIds($userIds, $date)
    {
        $query = $this->model->with(['stage', 'stage.project:id,name'])
            ->whereIn('user_id', $userIds);
        if (isset($date['from']) && isset($date['to'])) {
            $query->where(function ($qr) use ($date) {
                $qr->whereBetween('start_date', [$date['from'], $date['to']])
                    ->orWhereBetween('end_date', [$date['from'], $date['to']])
                    ->orWhere(function ($q) use ($date) {
                        $q->whereDate('start_date', '<', $date['from'])
                            ->whereDate('end_date', '>', $date['to']);
                    });
            });
        }

        return $query->get();
    }

    public function getAllocationByStageIds($stageIds, $date)
    {
        $query = $this->model->with(['stage', 'stage.project:id,name,project_type'])
            ->whereIn('stage_id', $stageIds);
        if (isset($date['from']) && isset($date['to'])) {
            $query->where(function ($qr) use ($date) {
                $qr->whereBetween('start_date', [$date['from'], $date['to']])
                    ->orWhereBetween('end_date', [$date['from'], $date['to']])
                    ->orWhere(function ($q) use ($date) {
                        $q->whereDate('start_date', '<', $date['from'])
                            ->whereDate('end_date', '>', $date['to']);
                    });
            });
        }
        return $query->get();
    }
    
    public function getRawAllocations($args)
    {
        $fromDate = $args['from_date'];
        $toDate = $args['to_date'];
        $query = $this->model->select('allocations.user_id', 'allocations.start_date',
                'allocations.end_date', 'allocations.allocation',
                'allocations.stage_id')
            ->when(isset($args['user_ids']), function ($q) use ($args) {
                $q->whereIn('allocations.user_id', $args['user_ids']);
            })
            ->when(isset($fromDate) && isset($toDate), function ($qr) use ($fromDate, $toDate) {
                $qr->whereBetween('allocations.start_date', [$fromDate, $toDate])
                ->orWhereBetween('allocations.end_date', [$fromDate, $toDate])
                ->orWhere(function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('allocations.start_date', '<', $fromDate)
                        ->whereDate('allocations.end_date', '>', $toDate);
                });
            })
            ->whereNull('allocations.deleted_at'); 
        return $query->get();
    }
    
    public function getAllocationInMonth($date)
    {
        $query = $this->model->with(['stage:id,project_id', 'stage.project:id,name'])
            ->whereBetween('start_date', [$date['from'], $date['to']])
            ->orWhereBetween('end_date', [$date['from'], $date['to']])
            ->orWhere(function ($q) use ($date) {
                $q->whereDate('start_date', '<', $date['from'])
                    ->whereDate('end_date', '>', $date['to']);
            });

        return $query->get();
    }

    public function getAllocationInYear($date)
    {
        $query = $this->model
            ->whereBetween('start_date', [$date['from'], $date['to']])
            ->orWhereBetween('end_date', [$date['from'], $date['to']])
            ->orWhere(function ($q) use ($date) {
                $q->whereDate('start_date', '<', $date['from'])
                    ->whereDate('end_date', '>', $date['to']);
            });

        return $query->get();
    }

    public function checkExistAllocationMember($memberId, $projectId)
    {
        return $this->model
            ->join('stages', 'stages.id', '=', 'allocations.stage_id')
            ->where('stages.project_id', $projectId)
            ->where('user_id', $memberId)
            ->count();
    }

    public function countDuplicateAllocation($args, $allocationId = null) {
        return $this->model
            ->where('stage_id', $args['stage_id'])
            ->where('user_id', $args['user_id'])
            ->where('role_id', $args['role_id'])
            ->where(function ($query) use ($args) {
                $query->orWhere(function ($q) use ($args) {
                    $q->whereDate('start_date', '>=', $args['start_date']);
                    $q->whereDate('end_date', '<=', $args['end_date']);
                });
                $query->orWhere(function ($query) use ($args) {
                    $query->where('start_date', '<=', $args['start_date']);
                    $query->where('end_date', '>=', $args['start_date']);
                });
                $query->orWhere(function ($query) use ($args) {
                    $query->where('start_date', '<=', $args['end_date']);
                    $query->where('end_date', '>=', $args['end_date']);
                });
            })
            ->when(!empty($allocationId), function ($query) use ($allocationId) {
                $query->where('id', '!=', $allocationId);
            })
            ->count();
    }

    public function getListAllocation($args = [])
    {
        return $this->model
            ->select(
                'allocations.id',
                'allocations.user_id',
                'allocations.start_date',
                'allocations.end_date',
                'allocation',
                'coefficient',
                'stages.type as stage_type',
                'stages.version as stage_version',
                'roles.name as role_name',
                'projects.id as project_id',
                'projects.name as project_name',
                'projects.division_id as division_id',
                'projects.project_type as project_type')
            ->join('stages', 'stages.id', '=', 'allocations.stage_id')
            ->join('roles', 'roles.id', '=', 'allocations.role_id')
            ->join('projects', 'projects.id', '=', 'stages.project_id')
            ->when(@$args['project_id'], function ($query) use ($args) {
                $query->whereIn('projects.id', (array)$args['project_id']);
            })
            ->when(@$args['project_types'], function ($query) use ($args) {
                $query->whereIn('projects.project_type', (array)$args['project_types']);
            })
            ->when(@$args['division_ids'], function ($query) use ($args) {
                $query->whereIn('projects.division_id', (array)$args['division_ids']);
            })
            ->when(@$args['stage_id'], function ($query) use ($args) {
                $query->whereIn('stage_id', (array)$args['stage_id']);
            })
            ->when(@$args['user_ids'], function ($query) use ($args) {
                $query->whereIn('user_id', (array)$args['user_ids']);
            })
            ->when(@$args['from'] && @$args['to'], function ($query) use ($args) {
                $query->where(function ($q) use ($args) {
                    $q->whereBetween('allocations.start_date', [$args['from'], $args['to']])
                        ->orWhereBetween('allocations.end_date', [$args['from'], $args['to']])
                        ->orWhere(function ($q) use ($args) {
                            $q->whereDate('allocations.start_date', '<', $args['from'])
                                ->whereDate('allocations.end_date', '>', $args['to']);
                        });
                });
            })
            ->whereNull('allocations.deleted_at')
            ->orderByDesc('projects.id')
            ->get();
    }

    public function getProjectAllocates($filters = [])
    {
        return $this->model
            ->selectRaw('projects.id as project_id, allocations.user_id, allocation, allocations.start_date, allocations.end_date, coefficient')
            ->join('stages', 'stages.id', '=', 'allocations.stage_id')
            ->join('projects', 'projects.id', '=', 'stages.project_id')
            ->when(!empty($filters['from']) && !empty($filters['to']), function ($query) use ($filters) {
                $query->whereBetween('allocations.start_date', [$filters['from'], $filters['to']])
                    ->orWhereBetween('allocations.end_date', [$filters['from'], $filters['to']])
                    ->orWhere(function ($q) use ($filters) {
                        $q->whereDate('allocations.start_date', '<', $filters['from'])
                            ->whereDate('allocations.end_date', '>', $filters['to']);
                    });
            })
            ->get();
    }
}
