<?php

namespace App\Repositories;

use App\Interfaces\ProjectSellerRepositoryInterface;
use App\Models\ProjectSeller;

class ProjectSellerRepository extends BaseRepository implements ProjectSellerRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return ProjectSeller::class;
    }

    public function getIdsByProject($projectIds)
    {
        return $this->select('*')->whereIn('project_id', $projectIds)->get();
    }
}
