<?php

namespace App\Repositories;

use App\Interfaces\TechnologyRepositoryInterface;
use App\Models\Technology;

class TechnologyRepository extends BaseRepository implements TechnologyRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Technology::class;
    }

    public function getTechnologies($keyword)
    {
        return $this->model
            ->where('programing_language', 'like', '%'.$keyword.'%')
            ->orWhere('framework', 'like', '%'.$keyword.'%%')
            ->get();
    }
}
