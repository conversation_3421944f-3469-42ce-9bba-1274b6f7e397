<?php

namespace App\Repositories;

use App\Interfaces\DivisionRepositoryInterface;
use App\Models\Division;

class DivisionRepository extends BaseRepository implements DivisionRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Division::class;
    }

    public function store($args)
    {
        return $this->model->create($args);
    }

    public function getAll($args)
    {
        return $this->model->orderBy('id', 'DESC')->get();
    }

    public function update($id, $args)
    {
        $data = $this->model->where('id', $id)->first();
        if (! $data) {
            return null;
        }
        $data->update($args);

        return $data;
    }

    public function destroy($id)
    {
        return $this->model->where('id', $id)->delete();
    }

    public function getDetail($id)
    {
        return $this->model->where('id', $id)->first();
    }
}
