<?php

namespace App\Repositories;

use App\Interfaces\FileEvidenceRepositoryInterface;
use App\Models\FileEvidence;

class FileEvidenceRepository extends BaseRepository implements FileEvidenceRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return FileEvidence::class;
    }

    public function store($args = arr): FileEvidence
    {
        return $this->model->create($args);
    }
}
