<?php

namespace App\Repositories;

use App\Interfaces\ProjectRepositoryInterface;
use App\Interfaces\DefectRepositoryInterface;
use App\Interfaces\FunctionCategoryRepositoryInterface;
use App\Interfaces\FunctionRepositoryInterface;
use App\Interfaces\QualityGateRepositoryInterface;
use App\Interfaces\SprintRepositoryInterface;
use App\Interfaces\StageRepositoryInterface;

class Repository
{
    /**
     * @return SprintRepository
     */
    public static function getSprint(): SprintRepository
    {
        return app(SprintRepositoryInterface::class);
    }

    /**
     * @return DefectRepository
     */
    public static function getDefect(): DefectRepository
    {
        return app(DefectRepositoryInterface::class);
    }

    /**
     * @return QualityGateRepository
     */
    public static function getQualityGate(): QualityGateRepository
    {
        return app(QualityGateRepositoryInterface::class);
    }

    /**
     * @return FunctionCategoryRepository
     */
    public static function getFunctionCategory(): FunctionCategoryRepository
    {
        return app(FunctionCategoryRepositoryInterface::class);
    }

    /**
     * @return FunctionRepository
     */
    public static function getFunction(): FunctionRepository
    {
        return app(FunctionRepositoryInterface::class);
    }

    /**
     * @return StageRepository
     */
    public static function getStage(): StageRepository
    {
        return app(StageRepositoryInterface::class);
    }

    /**
     * @return ProjectRepository
     */
    public static function getProject(): ProjectRepository
    {
        return app(ProjectRepositoryInterface::class);
    }

}
