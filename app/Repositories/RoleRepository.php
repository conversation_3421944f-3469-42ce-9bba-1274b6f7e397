<?php

namespace App\Repositories;

use App\Enums\ERole;
use App\Interfaces\RoleRepositoryInterface;
use App\Models\Role;

class RoleRepository extends BaseRepository implements RoleRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Role::class;
    }

    public function getByRoleNames($role_names)
    {
        return $this->whereIn('name', $role_names)->get()->pluck('id');
    }

    public function getRoles($args = [])
    {
        $isGetDl = $args['get_dl'] ?? 1;

        return $this->model
            ->when($isGetDl == 0, function ($query) {
                $query->whereNotIn('id', ERole::GROUP_DL);
            })
            ->get();
    }
}
