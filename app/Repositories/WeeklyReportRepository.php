<?php

namespace App\Repositories;

use App\Models\WeeklyReport;
use Illuminate\Support\Facades\Config;

class WeeklyReportRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return WeeklyReport::class;
    }

    public function getAll($args)
    {
        $query = $this->model
            ->when(@$args['week_start_date'], function ($query) use ($args) {
                return $query->where('week_start_date', $args['week_start_date']);
            })
            ->orderByDesc('id');

        if (isset($args['pagination']) && ($args['pagination'] == false || $args['pagination'] == 'false')) {
            return $query->get();
        }

        return $query->paginate($args['limit'] ?? Config::get('define.per_page_default'));
    }
}
