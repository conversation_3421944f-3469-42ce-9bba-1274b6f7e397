<?php

namespace App\Repositories;

use App\Models\ResourceRentalCosts;

class ResourceRentalCostRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return ResourceRentalCosts::class;
    }

    public function getWhere($filters = [], $columns = ['*'], $relationships = [])
    {
        return $this->model
            ->with($relationships)
            ->select($columns)
            ->when(isset($filters['project_ids']) ?? null, function ($query) use ($filters) {
                $query->whereIn('project_id', (array)$filters['project_ids']);
            })
            ->when(isset($filters['division_ids']) ?? null, function ($query) use ($filters) {
                $query->whereIn('division_id', (array)$filters['division_ids']);
            })
            ->when(isset($filters['project_division_ids']) ?? null, function ($query) use ($filters) {
                $query->whereHas('project', function ($q) use ($filters) {
                    $q->whereIn('division_id', (array)$filters['project_division_ids']);
                });
            })
            ->when(isset($filters['year']) ?? null, function ($query) use ($filters) {
                $query->where('year', $filters['year']);
            })
            ->when(isset($filters['ignore_ids']) ?? null, function ($query) use ($filters) {
                $query->whereNotIn('id', (array)$filters['ignore_ids']);
            })
            ->when(isset($filters['project_types']) ?? null, function ($query) use ($filters) {
                $query->whereHas('project', function ($q) use ($filters) {
                    $q->whereIn('project_type', (array)$filters['project_types']);
                });
            })
            ->get();
    }
}
