<?php

namespace App\Repositories;

use App\Interfaces\ProjectYearBudgetRepositoryInterface;
use App\Models\ProjectYearBudget;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class ProjectYearBudgetRepository extends BaseRepository implements ProjectYearBudgetRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return ProjectYearBudget::class;
    }

    public function getList($args = [])
    {
        return $this->model
            ->when(@$args['year'], fn($query) => $query->where('year', $args['year']))
            ->get();
    }

    public function store($args)
    {
        return $this->model->create($args);
    }

    public function updateBudget($args)
    {
        $projectBudget = $this->getDetail($args['project_id'], $args['year']);
        if ($projectBudget) {
            return $projectBudget->update($args);
        } else {
            return $this->model->create($args);
        }
    }

    public function getDetail($projectId, $year)
    {
        return $this->model
            ->where('project_id', $projectId)
            ->where('year', $year)
            ->first();
    }

    public function getByPeriod($projectId, $fromYear, $toYear)
    {
        return $this->model
            ->where('project_id', $projectId)
            ->whereBetween('year', [$fromYear, $toYear])
            ->get();
    }
}
