<?php

namespace App\Repositories;

use App\Interfaces\LevelRepositoryInterface;
use App\Models\Level;

class LevelRepository extends BaseRepository implements LevelRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Level::class;
    }

    public function store($args)
    {
        return $this->model->create($args);
    }

    public function getAll($args)
    {
        return $this->model->orderBy('order', 'asc')->get();
    }

    public function update($id, $args)
    {
        $data = $this->model->where('id', $id)->first();
        if (! $data) {
            return null;
        }
        $data->update($args);

        return $data;
    }

    public function destroy($id)
    {
        return $this->model->where('id', $id)->delete();
    }
}
