<?php

namespace App\Repositories;

use App\Interfaces\ProjectMonthBudgetRepositoryInterface;
use App\Models\ProjectMonthBudget;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class ProjectMonthBudgetRepository extends BaseRepository implements ProjectMonthBudgetRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return ProjectMonthBudget::class;
    }

    public function store($args)
    {
        return $this->model->create($args);
    }

    public function getList($projectId, $year)
    {
        return $this->model
            ->where('project_id', $projectId)
            ->where('year', $year)
            ->get();
    }

    public function deleteList($projectId, $year)
    {
        return $this->model
            ->where('project_id', $projectId)
            ->where('year', $year)
            ->delete();
    }

    public function getByPeriod($projectId, $fromMonth, $toMonth)
    {
        return $this->model
            ->where('project_id', $projectId)
            ->whereBetween('month', [$fromMonth, $toMonth])
            ->get();
    }

    public function getProjectBudgets($args = [])
    {
        return $this->model
            ->with('project:id,name,code,division_id,project_type,team_id')
            ->when(@$args['division_ids'], function ($query) use ($args) {
                return $query->whereHas('project', function ($query) use ($args) {
                    return $query->whereIn('division_id', (array)$args['division_ids']);
                });
            })
            ->when(@$args['team_ids'], function ($query) use ($args) {
                return $query->whereHas('project', function ($query) use ($args) {
                    return $query->whereIn('team_id', (array) $args['team_ids']);
                });
            })
            ->when(@$args['year'], function ($query) use ($args) {
                return $query->where('year', $args['year']);
            })
            ->when(@$args['project_ids'], function ($query) use ($args) {
                return $query->whereIn('project_id', (array)$args['project_ids']);
            })
            ->when(@$args['project_types'] ?? null, function ($query) use ($args) {
                $query->whereHas('project', function ($q) use ($args) {
                    $q->whereIn('project_type', (array)$args['project_types']);
                });
            })
            ->orderBy('project_id', 'desc')
            ->get();
    }
}
