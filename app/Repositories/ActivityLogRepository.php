<?php

namespace App\Repositories;

use App\Enums\EModelType;
use App\Enums\Enum;
use App\Interfaces\ActivityLogRepositoryInterface;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use Spatie\Activitylog\Models\Activity as ActivityLogModel;

class ActivityLogRepository extends BaseRepository implements ActivityLogRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return ActivityLogModel::class;
    }

    public function getList($args, $id)
    {
        $typeLog = EModelType::getActionString($args['log_type'] ?? null);

        return $this->model
            ->when(@$id, function ($query) use ($id) {
                return $query->where('subject_id', $id);
            })
            ->when(@$args['events'], function ($query) use ($args) {
                return $query->whereIn('event', $args['events']);
            })
            ->when(isset($args['user_id']), function ($query) use ($args) {
                return $query->where('causer_id', $args['user_id']);
            })
            ->when(isset($args['user_ids']), function ($query) use ($args) {
                return $query->whereIn('causer_id', $args['user_ids']);
            })
            ->when(isset($args['start_date']), function ($query) use ($args) {
                $date = date('Y-m-d', strtotime(trim($args['start_date']))).' '.Enum::START_TIME;

                return $query->where('created_at', '>=', $date);
            })
            ->when(isset($args['end_date']), function ($query) use ($args) {
                $date = date('Y-m-d', strtotime(trim($args['end_date']))).' '.Enum::END_TIME;

                return $query->whereDate('created_at', '<=', $date);
            })
            ->when(isset($args['keyword']), function ($query) use ($args) {
                return $query->where(function ($query) use ($args) {
                    if (Str::contains($args['keyword'], '%')) {
                        $operator = '=';
                    } else {
                        $operator = 'LIKE';
                        $args['keyword'] = "%{$args['keyword']}%";
                    }

                    return $query->where('description', $operator, $args['keyword']);
                });
            })
            ->orderByDesc('id')
            ->paginate(
                $args['per_page'] ?? Config::get('define.change_request_pagination'),
                ['*'],
                'page',
                $args['page'] ?? Config::get('define.page_default')
            );
    }

    public function getDetail($id)
    {
        return $this->model->where('id', $id)->first();
    }
}
