<?php

namespace App\Repositories;

use App\Enums\ECommon;
use App\Interfaces\QualityGateRepositoryInterface;
use App\Models\QualityGate;

class QualityGateRepository extends BaseRepository implements QualityGateRepositoryInterface
{
    protected $columns = [
        'id',
        'project_id',
        'sprint_id',
        'quality_gate',
        'number_of_non_compliance',
        'number_of_process',
        'number_of_incident',
        'number_of_customer_complaint',
        'note',
        'created_at',
    ];

    protected $relations = [];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * {@inheritDoc}
     */
    public function model()
    {
        return QualityGate::class;
    }

    public function getByProject($projectIds)
    {
        $condition = [
            'project_id' => $projectIds,
        ];

        return $this->list($condition, $this->columns, ECommon::BUILDER_TYPE_ELOQUENT, $this->relations);
    }

    /**
     * Get project-level quality gate (where sprint_id is null)
     *
     * @param int $projectId
     * @return QualityGate|null
     */
    public function getProjectQualityGate($projectId)
    {
        return $this->model->where('project_id', $projectId)
            ->whereNull('sprint_id')
            ->first();
    }

    /**
     * Check if project already has a quality gate
     *
     * @param int $projectId
     * @return bool
     */
    public function hasProjectQualityGate($projectId)
    {
        return $this->model->where('project_id', $projectId)
            ->whereNull('sprint_id')
            ->exists();
    }

    /**
     * Create or update project quality gate
     *
     * @param array $data
     * @return QualityGate
     */
    public function storeProjectQualityGate($data)
    {
        // Ensure sprint_id is null for project-level quality gates
        $data['sprint_id'] = null;

        return $this->updateOrCreate(
            ['project_id' => $data['project_id'], 'sprint_id' => null],
            $data
        );
    }
}
