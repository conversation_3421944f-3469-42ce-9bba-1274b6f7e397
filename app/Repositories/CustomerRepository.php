<?php

namespace App\Repositories;

use App\Interfaces\CustomerRepositoryInterface;
use App\Models\Customer;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class CustomerRepository extends BaseRepository implements CustomerRepositoryInterface
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return Customer::class;
    }

    public function getCustomers($fields, $keyword, $customerId)
    {
        $query = $this->model->select('id', 'name', 'email');
        if (! empty($customerId)) {
            return $query->when($customerId, function ($q, $customerId) {
                return $q->whereIn('id', $customerId);
            })->get();
        }

        $selectedFields = $fields ?? '*';
        return $query->select($selectedFields)
            ->when($keyword, function ($query) use ($keyword) {
                return $query->where('name', 'like', '%' . $keyword . '%')
                    ->orWhere('original_name', 'like', '%' . $keyword . '%')
                    ->orWhere('code', 'like', '%' . $keyword . '%')
                    ->orWhere('email', 'like', '%' . $keyword . '%');
            })
            ->orderByDesc('id')
            ->get();
    }

    public function getListCustomer($args = arr)
    {
        $limit = $args['limit'] ?? null;
        $keyword = $args['keyword'] ?? null;
        $relation = $args['relation'] ?? [];

        return $this->model->with('projects')
            ->with($relation)
            ->when($keyword, function ($query) use ($keyword) {
                return $query->where('name', 'like', '%' . $keyword . '%')
                    ->orWhere('original_name', 'like', '%' . $keyword . '%')
                    ->orWhere('code', 'like', '%' . $keyword . '%')
                    ->orWhere('email', 'like', '%' . $keyword . '%');
            })
            ->orderByDesc('id')
            ->paginate($limit ?? Config::get('define.customerPagination'));
    }

    public function store($args = arr)
    {
        return $this->model->create($args);
    }

    public function update($id, $args = arr)
    {
        $customer = $this->model->where('id', $id)->first();
        if (! $customer) {
            return null;
        }

        $customer->update($args);

        return $customer;
    }

    public function destroy($id)
    {
        return $this->model->where('id', $id)->delete();
    }

    public function getDivisionCustomers($divisionId)
    {
        $query = $this->model->select(
                DB::raw('distinct customers.id, customers.name '),
            )
        ->join('customer_project', 'customers.id', '=', 'customer_project.customer_id')
        ->join('projects', 'projects.id', '=', 'customer_project.project_id')
        ->whereNull('projects.deleted_at')
        ->where('projects.division_id', $divisionId);
        return $query->get();
    }
}
