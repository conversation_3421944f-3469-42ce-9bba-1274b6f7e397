<?php

namespace App\Services;

use App\Enums\ECommon;
use App\Repositories\Repository;

class FunctionService
{
    /**
     * Get functions by project id with pagination
     *
     * @param  array  $params
     * @return mixed
     */
    public function list(array $params)
    {
        $conditions = $this->getConditions($params);

        return Repository::getFunction()->listPagination(
            $conditions,
            ['*'],
            ECommon::BUILDER_TYPE_ELOQUENT,
            ['functionCategory', 'sprint']
        );
    }

    /**
     * Get function by id
     *
     * @param  int  $id
     * @return mixed
     */
    public function show(int $id)
    {
        return Repository::getFunction()->with(['functionCategory', 'sprint'])->find($id);
    }

    /**
     * Create a new function
     *
     * @param  array  $data
     * @return mixed
     */
    public function store($data)
    {
        return Repository::getFunction()->create($data);
    }

    /**
     * Update function
     *
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function update($id, $data)
    {
        return Repository::getFunction()->update($id, $data);
    }

    /**
     * Delete function
     *
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        return Repository::getFunction()->delete($id);
    }

    /**
     * Create conditions for query
     *
     * @param  array  $params
     * @return array
     */
    private function getConditions(array $params)
    {
        $conditions = [
            'page' => $params['page'] ?? 1,
            'per_page' => $params['per_page'] ?? 10,
            'search' => $params['search'] ?? null,
            'order_by' => $params['order_by'] ?? 'id',
            'order_type' => $params['order_type'] ?? 'desc',
        ];

        if (isset($params['project_id'])) {
            $conditions['project_id'] = $params['project_id'];
        }

        if (isset($params['sprint_id'])) {
            $conditions['sprint_id'] = $params['sprint_id'];
        }

        if (isset($params['function_category_id'])) {
            $conditions['function_category_id'] = $params['function_category_id'];
        }

        return $conditions;
    }
}
