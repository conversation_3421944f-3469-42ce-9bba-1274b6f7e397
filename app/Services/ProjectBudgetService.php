<?php

namespace App\Services;

use App\Interfaces\ProjectYearBudgetRepositoryInterface;
use App\Interfaces\ProjectMonthBudgetRepositoryInterface;
use Carbon\Carbon;

class ProjectBudgetService
{
    private $projectYearBudgetRepository;

    private $projectMonthBudgetRepository;

    public function __construct(
        ProjectYearBudgetRepositoryInterface $projectYearBudgetRepository,
        ProjectMonthBudgetRepositoryInterface $projectMonthBudgetRepository
    ) {
        $this->projectYearBudgetRepository = $projectYearBudgetRepository;
        $this->projectMonthBudgetRepository = $projectMonthBudgetRepository;
    }

    function getYearParams($args)
    {
        return [
            'project_id' => $args['project_id'],
            'year' => $args['year'],
            'budget' => $args['year_budget'] ?? null
        ];
    }

    function getMonthParams($args)
    {
        $monthBudget = $args['month_budget'];
        $monthParams = collect($monthBudget)
            ->filter(function ($item) {
                return $item && isset($item['budget']);
            })
            ->map(function ($item) use ($args) {
                return [
                    'project_id' => $args['project_id'],
                    'year' => $args['year'],
                    'month' => $item['month'],
                    'budget' => $item['budget'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            })->toArray();
        return $monthParams;
    }

    public function updateProjectBudget($args)
    {
        $projectId = $args['project_id'];
        $year = $args['year'];
        $yearParams = $this->getYearParams($args);
        $monthParams = $this->getMonthParams($args);
        if (isset($yearParams['budget'])) {
            $this->projectYearBudgetRepository->updateBudget($yearParams);
        }
        $this->projectMonthBudgetRepository->deleteList($projectId, $year);
        $this->projectMonthBudgetRepository->insert($monthParams);
        return $this->getDetail($projectId, $year);
    }

    function formatMonthBudgetList($year, $monthBudget)
    {
        $existingMonths = collect($monthBudget)->keyBy('month')->toArray();
        $result = collect(range(1, 12))->map(function ($monthIndex) use ($year, $existingMonths) {
            $key = $monthIndex < 10 ? $year . '-0' . $monthIndex : $year . '-' . $monthIndex;
            $existingData = $existingMonths[$key] ?? null;
            return [
                'month' => $key,
                'budget' => isset($existingData) ? $existingData['budget'] : null
            ];
        });
        return $result;
    }

    public function getDetail($projectId, $year)
    {
        $yearBudget = $this->projectYearBudgetRepository->getDetail($projectId, $year);
        $monthBudget = $this->projectMonthBudgetRepository->getList($projectId, $year);
        $result = [
            'project_id' => intval($projectId),
            'year' => $year,
            'year_budget' => $yearBudget['budget'] ?? null,
            'month_budget' => $this->formatMonthBudgetList($year, $monthBudget)
        ];
        return $result;
    }

    public function getBudgetOfProject($projectId, $fromMonth, $toMonth)
    {
        $fromYear = createFromFormat($fromMonth, 'Y-m')->year;
        $toYear = createFromFormat($toMonth, 'Y-m')->year;
        $yearBudget = $this->projectYearBudgetRepository->getByPeriod($projectId, $fromYear, $toYear);
        $monthBudget = $this->projectMonthBudgetRepository->getByPeriod($projectId, $fromMonth, $toMonth);
        $result = [
            'project_id' => intval($projectId),
            'year_budget' => $yearBudget,
            'month_budget' => $monthBudget
        ];
        return $result;
    }

    public function getProjectBudgets($args = [])
    {
        return [
            'year_budget' => $this->projectYearBudgetRepository->getList($args),
            'month_budget' => $this->projectMonthBudgetRepository->getProjectBudgets($args)
        ];
    }
}
