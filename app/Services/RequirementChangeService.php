<?php

namespace App\Services;

use App\Enums\EFileEvidenceType;
use App\Interfaces\FileEvidenceRepositoryInterface;
use App\Interfaces\RequirementChangeRepositoryInterface;
use Carbon\Carbon;

class RequirementChangeService
{
    private RequirementChangeRepositoryInterface $requirementChangeRepository;

    private FileEvidenceRepositoryInterface $fileEvidenceRepository;

    private FileService $fileService;

    public function __construct(RequirementChangeRepositoryInterface $requirementChangeRepository, FileEvidenceRepositoryInterface $fileEvidenceRepository, FileService $fileService)
    {
        $this->requirementChangeRepository = $requirementChangeRepository;
        $this->fileEvidenceRepository = $fileEvidenceRepository;
        $this->fileService = $fileService;
    }

    public function getList($args, $projectId)
    {
        return $this->requirementChangeRepository->getList($args, $projectId);
    }

    public function store($args)
    {
        $requirementChange = $this->requirementChangeRepository->store($args);
        if (! $requirementChange) return false;

        $dataInsert = $this->createFileEvidence(
            $args['evidence_customer'] ?? null,
            $args['evidence_finished'] ?? null,
            $requirementChange->id
        );
        if (! empty($dataInsert)) $requirementChange->file_evidences()->insert($dataInsert);
        $requirementChange->load('file_evidences')->fresh();

        return $requirementChange;
    }

    public function update($id, $args)
    {
        $requirementChange = $this->requirementChangeRepository->update($id, $args);
        if (! $requirementChange) return false;

        // delete documents
        if (isset($args['delete_evidence_ids'])) {
            $files = $this->fileEvidenceRepository
                ->whereIn('id', $args['delete_evidence_ids'])
                ->get();
            $urls = $files->map(fn ($file) => $file->getRawOriginal('url'))->toArray();
            $fileIds = $files->pluck('id')->toArray();
            $this->fileService->deleteMultipleFileS3($urls);
            $this->fileEvidenceRepository->delete($fileIds);
        }

        $dataInsert = $this->createFileEvidence(
            $args['evidence_customer'] ?? null,
            $args['evidence_finished'] ?? null,
            $requirementChange->id
        );
        if (! empty($dataInsert)) $requirementChange->file_evidences()->insert($dataInsert);
        $requirementChange->load('file_evidences')->fresh();

        return $requirementChange;
    }

    public function destroy($deleteIds)
    {

        return (bool) $this->requirementChangeRepository->destroy($deleteIds);
    }

    public function getDetail($id)
    {
        return $this->requirementChangeRepository->getDetail($id);
    }

    public function import($dataImport)
    {
        return $this->requirementChangeRepository->insert($dataImport);
    }

    public function createFileEvidence($evidenceCustomer, $evidenceFinished, $requirementChangeId)
    {
        $dataInsert = [];
        if (isset($evidenceCustomer)) {
            foreach ($evidenceCustomer as $key => $file) {
                $item['file_name'] = $file['file_name'];
                $item['requirement_change_id'] = $requirementChangeId;
                $item['url'] = $file['url'];
                $item['file_type'] = EFileEvidenceType::FILE_EVIDENCE_CUSTOMER;
                $item['created_at'] = Carbon::now();
                $item['updated_at'] = Carbon::now();
                array_push($dataInsert, $item);
            }
        }

        if (isset($evidenceFinished)) {
            foreach ($evidenceFinished as $key => $file) {
                $item['file_name'] = $file['file_name'];
                $item['requirement_change_id'] = $requirementChangeId;
                $item['url'] = $file['url'];
                $item['file_type'] = EFileEvidenceType::FILE_EVIDENCE_FINISHED;
                $item['created_at'] = Carbon::now();
                $item['updated_at'] = Carbon::now();
                array_push($dataInsert, $item);
            }
        }

        return $dataInsert;
    }
}
