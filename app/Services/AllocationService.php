<?php

namespace App\Services;

use App\Interfaces\AllocationRepositoryInterface;
use App\Interfaces\ProjectUserRoleRepositoryInterface;
use App\Interfaces\StageRepositoryInterface;
use App\Repositories\UserUnpaidLeaveRepository;
use Carbon\Carbon;
use Illuminate\Http\Response;

class AllocationService
{
    private $allocationRepository;
    private $projectUserRoleRepository;
    private $stageRepository;
    private $userUnpaidLeaveRepository;

    public function __construct(
        AllocationRepositoryInterface      $allocationRepository,
        ProjectUserRoleRepositoryInterface $projectUserRoleRepository,
        StageRepositoryInterface           $stageRepository,
        UserUnpaidLeaveRepository           $userUnpaidLeaveRepository
    ) {
        $this->allocationRepository = $allocationRepository;
        $this->projectUserRoleRepository = $projectUserRoleRepository;
        $this->stageRepository = $stageRepository;
        $this->userUnpaidLeaveRepository = $userUnpaidLeaveRepository;
    }

    public function store($args, $stageId)
    {
        $allocations = [];
        foreach ($args['user_ids'] as $key => $userId) {
            $allocationArgs['user_id'] = $userId;
            $allocationArgs['stage_id'] = $stageId;
            $allocationArgs['skill_id'] = $args['skill_ids'][$key];
            $allocationArgs['role_id'] = $args['role_ids'][$key];
            $allocationArgs['start_date'] = $args['start_dates'][$key];
            $allocationArgs['end_date'] = $args['end_dates'][$key];
            $allocationArgs['allocation'] = $args['allocations'][$key];
            $allocationArgs['man_day'] = $args['man_day'][$key];
            $allocationArgs['coefficient'] = $args['coefficients'][$userId];
            $allocationArgs['created_at'] = now();
            $allocationArgs['updated_at'] = now();
            $allocations[] = $allocationArgs;
        }

        $allocationAmount = count($allocations);
        $result = $this->allocationRepository->insert($allocations);
        if ($result) {
            $allocations = $this->allocationRepository
                ->take($allocationAmount)
                ->orderByDesc('created_at')
                ->get();
        }

        return $allocations;
    }

    public function getAllocations($member_ids, $stageId)
    {
        $allocations = $this->allocationRepository->getAllocations($member_ids, $stageId);
        $allocations->filter(fn ($allocation) => isset($allocation->stage->project))
            ->map(function ($allocation) {
                $allocation->project = $allocation->stage->project;
                unset($allocation->stage);
            });

        return $allocations;
    }

    public function update($allocationId, $args)
    {
        return $this->allocationRepository->update($allocationId, $args);
    }

    public function destroy($id)
    {
        $result = $this->allocationRepository->destroy($id);

        return (bool) $result;
    }

    public function getAllocationByUserIds($userIds, $date)
    {
        $allocates = $this->allocationRepository->getAllocationByUserIds($userIds, $date);
        $allocates->map(function ($allocate) {
            $allocate->stage_name = $allocate->stage->name ?? '';
            if (isset($allocate->stage->project->name)) {
                $allocate->project = $allocate->stage->project->name;
                $allocate->project_id = $allocate->stage->project->id;
            }
            unset($allocate->stage);

            return $allocate;
        });

        return $allocates;
    }

    public function getAllocationByStageIds($stageIds, $date)
    {
        $allocates = $this->allocationRepository->getAllocationByStageIds($stageIds, $date);
        $allocates->map(function ($allocate) {
            $allocate->stage_name = $allocate->stage->name;
            if (isset($allocate->stage->project->name)) {
                $project['name'] = $allocate->stage->project->name;
                $project['id'] = $allocate->stage->project->id;
                $project['type'] = $allocate->stage->project->project_type;
                $allocate->project = $project;
            }
            unset($allocate->stage);

            return $allocate;
        });

        return $allocates;
    }

    public function getAllocationInMonth($date)
    {
        $allocates = $this->allocationRepository->getAllocationInMonth($date);
        $allocates->map(function ($allocate) {
            if (isset($allocate->stage->project->name)) {
                $allocate->project = $allocate->stage->project->name;
            }
            unset($allocate->stage);

            return $allocate;
        });

        return $allocates;
    }

    public function getAllocationInYear($date)
    {
        $allocates = $this->allocationRepository->getAllocationInYear($date);

        return $allocates;
    }

    public function checkExistUser($userIds, $projectId)
    {
        return $this->projectUserRoleRepository->checkExistUser($userIds, $projectId);
    }

    public function find($id)
    {
        return $this->allocationRepository->find($id);
    }

    function isDuplicatedInParams($args)
    {
        $userIds = $args['user_ids'] ?? [];
        $roleIds = $args['role_ids'] ?? [];
        $startDates = $args['start_dates'] ?? [];
        $endDates = $args['end_dates'] ?? [];
        $isDuplicatedInParams = collect($userIds)->contains(function ($userId, $index) use ($userIds, $roleIds, $startDates, $endDates) {
            return collect($userIds)->contains(function ($otherId, $otherIndex) use ($userId, $index, $userIds, $roleIds, $startDates, $endDates) {
                return $otherIndex !== $index && $otherId === $userId && $roleIds[$otherIndex] === $roleIds[$index] && (
                    ($startDates[$index] >= $startDates[$otherIndex] && $endDates[$index] <= $endDates[$otherIndex]) ||
                    ($startDates[$index] <= $startDates[$otherIndex] && $startDates[$otherIndex] <= $endDates[$index]) ||
                    ($startDates[$index] <= $endDates[$otherIndex] && $endDates[$otherIndex] <= $endDates[$index])
                );
            });
        });
        return $isDuplicatedInParams;
    }

    public function isValidAllocations($args, $stageId, $allocationId = null)
    {
        $userIds = $args['user_ids'] ?? [];
        $roleIds = $args['role_ids'] ?? [];
        $startDates = $args['start_dates'] ?? [];
        $endDates = $args['end_dates'] ?? [];

        return !$this->isDuplicatedInParams($args) && collect($userIds)->every(function ($userId, $index) use (
            $roleIds,
            $stageId,
            $startDates,
            $endDates,
            $allocationId
        ) {
            return $this->allocationRepository->countDuplicateAllocation([
                'user_id' => $userId,
                'role_id' => $roleIds[$index],
                'start_date' => $startDates[$index],
                'end_date' => $endDates[$index],
                'stage_id' => $stageId,
            ], $allocationId) == 0;
        });
    }

    // public function isValidUserPaidLeave($args, $stageId, $allocationId = null)
    // {
    //     $userIds = $args['user_ids'] ?? [];
    //     $startDates = $args['start_dates'] ?? [];
    //     $endDates = $args['end_dates'] ?? [];

    //     return collect($userIds)->every(function ($userId, $index) use ($roleIds, $stageId, $startDates, $endDates, $allocationId) {
    //         return $this->allocationRepository->countDuplicateAllocation([
    //                 'user_id' => $userId,
    //                 'role_id' => $roleIds[$index],
    //                 'start_date' => $startDates[$index],
    //                 'end_date' => $endDates[$index],
    //                 'stage_id' => $stageId,
    //             ], $allocationId) == 0;
    //     });
    // }

    public function checkTimeStage($args, $stageId)
    {
        $stage = $this->stageRepository->find($stageId);
        if (!$stage) {
            return [
                'message' => __('not_found'),
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            ];
        }
        foreach ($args['user_ids'] as $key => $userId) {
            $allocationStartDate = $args['start_dates'][$key];
            $allocationEndDate = $args['end_dates'][$key];
            $checkTimeStage = $this->checkTimeStageForRange($stage, $allocationStartDate, $allocationEndDate);
            if (!$checkTimeStage) {
                return [
                    'message' => __('expected_date_is_between_the_end_date_delivery'),
                    'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
                ];
            }
        }

        return false;
    }

    function checkTimeStageForRange($stage, $start, $end)
    {
        if (isset($stage->replan_start_date) && isset($stage->replan_end_date)) {
            $startDate = createFromFormat($stage->replan_start_date);
            $endDate = createFromFormat($stage->replan_end_date);
        } else {
            $startDate = createFromFormat($stage->start_date);
            $endDate = createFromFormat($stage->end_date);
        }
        $allocationStartDate = createFromFormat($start);
        $allocationEndDate = createFromFormat($end);
        return ($allocationStartDate->between($startDate, $endDate) &&
            $allocationEndDate->between($startDate, $endDate)
        );
    }

    public function getListAllocation($args = [])
    {
        return $this->allocationRepository->getListAllocation($args);
    }
    
    public function getRawAllocations($args = [])
    {
        return $this->allocationRepository->getRawAllocations($args);
    }

    public function getAllocationIn2023()
    {
        $date = [
            'from' => Carbon::now()->firstOfYear()->format('Y-m-d'),
            'to' => Carbon::now()->endOfYear()->format('Y-m-d')
        ];
        return $this->allocationRepository->getAllocationInYear($date);
    }

    public function getAll()
    {
        return $this->allocationRepository->get();
    }

    public function checkDuplicateUnpaidLeave($userIds, $startDates, $endDates): bool
    {
        return collect($userIds)->contains(function ($userId, $key) use ($startDates, $endDates) {
            return $this->isExistUnpaidLeave($userId, $startDates[$key], $endDates[$key]);
        });
    }

    public function isExistUnpaidLeave($userId, $startDate, $endDate)
    {
        return $this->userUnpaidLeaveRepository->isExistUnpaidLeave(
            $userId,
            $startDate,
            $endDate
        );
    }
}
