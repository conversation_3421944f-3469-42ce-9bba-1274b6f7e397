<?php

namespace App\Services;

use App\Enums\EDailyReport;
use App\Enums\Enum;
use App\Enums\EStaffContractType;
use App\Traits\UserData;
use App\Traits\DivisionData;
use App\Repositories\AllocationRepository;
use App\Repositories\DailyReportRepository;
use App\Repositories\ProjectUserRoleRepository;
use App\Repositories\StageRepository;
use App\Repositories\UserUnpaidLeaveRepository;
use App\Traits\Helper;
use Carbon\Carbon;

class DailyReportService
{
    use Helper, UserData, DivisionData;

    protected DailyReportRepository $dailyReportRepository;
    protected ProjectUserRoleRepository $projectUserRoleRepository;
    protected UserUnpaidLeaveRepository $userUnpaidLeaveRepository;
    protected AllocationRepository $allocationRepository;
    protected StageRepository $stageRepository;

    public function __construct(
        DailyReportRepository     $dailyReportRepository,
        ProjectUserRoleRepository $projectUserRoleRepository,
        UserUnpaidLeaveRepository $userUnpaidLeaveRepository,
        AllocationRepository $allocationRepository,
        StageRepository $stageRepository,
    ) {
        $this->dailyReportRepository = $dailyReportRepository;
        $this->projectUserRoleRepository = $projectUserRoleRepository;
        $this->userUnpaidLeaveRepository = $userUnpaidLeaveRepository;
        $this->allocationRepository = $allocationRepository;
        $this->stageRepository = $stageRepository;
    }

    public function getList($args)
    {
        if (@$args['get_all']) {
            $args['from_date'] = null;
            $args['to_date'] = null;
            $args['stage_id'] = null;
        } else {
            $stage = @$args['stage_id'] ? $this->stageRepository->find($args['stage_id']) : null;

            if ($stage) {
                $args['from_date'] = !empty($stage->replan_start_date) ? $stage->replan_start_date : $stage->start_date;
                $args['to_date'] = !empty($stage->replan_end_date) ? $stage->replan_end_date : $stage->end_date;
            }
        }

        $dailyReports = $this->dailyReportRepository->getList($args, [
            'id',
            'user_id',
            'project_id',
            'issue_key',
            'title',
            'work_date',
            'actual_time',
            'description',
            'link_backlog',
            'status',
            'reject_reason',
            'process_type'
        ]);

        if (!empty($args['is_all']) && $args['is_all']) {
            return $dailyReports;
        }

        $projectUserIds = $this->projectUserRoleRepository
            ->getIdUsersProject($args['project_id'])
            ->pluck('user_id')
            ->toArray();

        $userHasReport = $this->getUserHasReport($args)
            ->groupBy('user_id')
            ->map(function ($item) {
                return $item->flatten(1)->pluck('work_date')->unique()->values()->toArray();
            });

        $datesInRange = $this->getDatesInRange($args['from_date'], $args['to_date'], true);

        $userIdHasAllReportInRange = $userHasReport->filter(function ($item) use ($datesInRange) {
            return $this->isSameArray($item, $datesInRange);
        })
            ->keys()
            ->toArray();

        $group = $args['group'] ?? EDailyReport::GROUP_BY_USER;
        $dailyReports = $this->processDailyReport($dailyReports, $group);

        $pendingQuantity = $this->dailyReportRepository->getList(
            array_replace($args, ['status' => EDailyReport::PENDING_STATUS]),
            ['id']
        )->count();

        $successQuantity = $this->dailyReportRepository->getList(
            array_replace($args, ['status' => EDailyReport::SUCCESS_STATUS]),
            ['id']
        )->count();

        $rejectQuantity = $this->dailyReportRepository->getList(
            array_replace($args, ['status' => EDailyReport::REJECT_STATUS]),
            ['id']
        )->count();

        return [
            'daily_reports' => $dailyReports,
            'user_has_report' => $userIdHasAllReportInRange,
            'user_not_report' => array_values(array_diff($projectUserIds, $userIdHasAllReportInRange)),
            'total_time_success_in_date' => $this->totalTimeSuccessInDate($args),
            'total_allocate_time_in_date' => $this->totalAllocateTimeInDate($args),
            'pending_quantity' => $pendingQuantity,
            'success_quantity' => $successQuantity,
            'reject_quantity' => $rejectQuantity
        ];
    }

    public function processDailyReport($dailyReports, $group = EDailyReport::GROUP_BY_USER)
    {
        $keyGroup = $group == EDailyReport::GROUP_BY_USER ? 'user_id' : 'work_date';

        return $dailyReports
            ->groupBy($keyGroup)
            ->map(function ($userDailyReports, $key) use ($keyGroup) {
                return [
                    $keyGroup => $key,
                    'total_task' => $userDailyReports->count(),
                    'reports' => $userDailyReports->toArray()
                ];
            })
            ->values();
    }

    protected function totalTimeSuccessInDate($args)
    {
        return $this->dailyReportRepository->getTotalActualTimes(
            $args['project_id'],
            @$args['from_date'],
            @$args['to_date'],
            EDailyReport::SUCCESS_STATUS,
        )
            ->groupBy('user_id')
            ->map(function ($item) {
                return $item
                    ->keyBy('work_date')
                    ->map(function ($item) {
                        return $item->total_time;
                    });
            })
            ->toArray();
    }

    protected function totalAllocateTimeInDate($args)
    {
        return $this->allocationRepository->getListAllocation($args)
            ->groupBy('user_id')
            ->map(function ($userAllocates) use ($args) {
                $allocateTimes = collect($userAllocates)->map(function ($allocate) {
                    $allocateTime = $allocate->allocation * 8 / 100;
                    $dateRange = $this->getDatesInRange($allocate->start_date, $allocate->end_date, true);

                    return collect($dateRange)->flip()->map(fn ($date) => $allocateTime);
                })->toArray();

                $allocateTimeSums = [];

                foreach ($allocateTimes as $value1) {
                    foreach ($value1 as $date => $value2) {
                        $allocateTimeSums[$date] = ($allocateTimeSums[$date] ?? 0) + $value2;
                    }
                }

                if (@$args['from_date'] && @$args['to_date']) {
                    return collect($allocateTimeSums)->filter(function ($value, $date) use ($args) {
                        return $date >= $args['from_date'] && $date <= $args['to_date'];
                    });
                }

                return collect($allocateTimeSums);
            })
            ->filter(fn ($item) => $item->isNotEmpty())
            ->toArray();
    }

    protected function getUserHasReport($args)
    {
        $args['status'] = EDailyReport::REPORTED_STATUSES;

        return $this->dailyReportRepository->getList($args);
    }

    public function getReports($args, $columns = null)
    {
        $columns = $args['columns'] ?? ['*'];

        return $this->dailyReportRepository->getList($args, $columns);
    }

    public function isValidDateDailyReport($workDate)
    {
        $now = now()->format('Y-m-d');

        // if (Carbon::parse($workDate)->isWeekend()) {
        //     return false;
        // }
        
        $lateLogWorkAllowDate = env('LATE_LOG_ALLOWED_DATE', 5);
        $isLateLogAllowedDate = $this->isLateLogAllowedDate(
            $workDate,
            $lateLogWorkAllowDate
        );


        return $workDate == $now || $isLateLogAllowedDate && $workDate < $now;
    }

    public function checkDateInDailyReports($dailyReports)
    {
        $errors = [];

        foreach ($dailyReports as $dailyReport) {
            $isValidDateDailyReport = $this->isValidDateDailyReport($dailyReport['work_date']);

            if (!$isValidDateDailyReport) {
                $errors[] = [
                    'user_id' => $dailyReport['user_id'],
                    'work_date' => $dailyReport['work_date']
                ];
            }
        }

        return $errors;
    }

    public function checkUnpaidLeaveDuplicates($dailyReports)
    {
        $errors = [];

        foreach ($dailyReports as $dailyReport) {
            $isUnpaidLeaveDuplicate = $this->isUnpaidLeaveDuplicate(
                $dailyReport['user_id'],
                $dailyReport['work_date']
            );

            if ($isUnpaidLeaveDuplicate) {
                $errors[] = [
                    'user_id' => $dailyReport['user_id'],
                    'work_date' => $dailyReport['work_date']
                ];
            }
        }

        return $errors;
    }

    public function isUnpaidLeaveDuplicate($userId, $workDate): bool
    {
        return (bool)$this->userUnpaidLeaveRepository->getList([
            'user_id' => $userId,
            'start_date' => $workDate,
            'end_date' => $workDate
        ])->count();
    }

    function isLateLogAllowedDate($workDate, $dayAgo): bool
    {
        $targetDate = now();
        $workingDays = 0;

        while ($workingDays < $dayAgo) {
            $targetDate->subDay();

            if ($targetDate->isWeekday()) {
                $workingDays++;
            }
        }

        $targetDate = $targetDate->toDateString();

        return Carbon::parse($workDate)->gte($targetDate);
    }

    public function getTotalHourOfUserInDay($userId, $date, $id = null)
    {
        return $this->dailyReportRepository->sumWhere(
            function ($query) use ($userId, $date, $id) {
                $query->where('user_id', $userId)
                    ->where('work_date', $date)
                    ->whereIn('status', [
                        EDailyReport::PENDING_STATUS,
                        EDailyReport::SUCCESS_STATUS
                    ])
                    ->when($id, function ($query) use ($id) {
                        $query->where('id', '!=', $id);
                    });
            },
            'actual_time'
        );
    }

    public function checkDailyReportsWorkHours($dailyReports)
    {
        $errors = [];
        $dates = array_column($dailyReports, 'work_date');
        $userId = $dailyReports[0]['user_id'];

        // current data
        $currentDateWorkHours = collect($dailyReports)
            ->groupBy('work_date')
            ->map(function ($item) {
                return $item->sum('actual_time');
            })
            ->toArray();

        // data in database
        $availableDateWorkHours = $this->dailyReportRepository->getUserWorkDateHour($userId, $dates);

        if ($availableDateWorkHours) {
            $availableDateWorkHours = $availableDateWorkHours->pluck('hour', 'work_date')->toArray();
        }

        foreach ($currentDateWorkHours as $date => $hour) {
            $availableHour = $availableDateWorkHours[$date] ?? 0;

            if ($hour + $availableHour > EDailyReport::WORKING_HOURS_IN_DAY) {
                $errors[] = [
                    'work_date' => $date
                ];
            }
        }

        return $errors;
    }


    public function store($args)
    {
        $data = array_map(function ($args) {
            return [
                'user_id' => $args['user_id'],
                'coefficient' => $args['coefficient'] ?? null,
                'project_id' => $args['project_id'],
                'title' => $args['title'],
                'link_backlog' => $args['link_backlog'] ?? null,
                'work_date' => $args['work_date'],
                'process_type' => $args['process_type'],
                'actual_time' => $args['actual_time'] ?? null,
                'description' => $args['description'] ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }, $args);

        return $this->dailyReportRepository->insert($data);
    }

    public function update($id, $args)
    {
        $args['status'] = EDailyReport::PENDING_STATUS;

        return $this->dailyReportRepository->update($id, $args);
    }

    public function destroy($id)
    {
        return (bool) $this->dailyReportRepository->destroy($id);
    }

    public function find($id)
    {
        return $this->dailyReportRepository->find($id);
    }

    public function changeMultiDailyReportStatus($dailyReportIds, $newStatus, $rejectReason)
    {
        $result = $this->dailyReportRepository->updateWhere(function ($query) use ($dailyReportIds) {
            $query->whereIn('id', (array)$dailyReportIds);
            $query->where('status', EDailyReport::PENDING_STATUS);
        }, [
            'status' => $newStatus,
            'reject_reason' => $rejectReason
        ]);
        $infoDailyReports = $this->dailyReportRepository->getList([
            'ids' => $dailyReportIds,
            'get_project' => 0
        ], [
            'user_id',
            'work_date'
        ])->groupBy('user_id')->map(function ($dailyReport) {
            return $dailyReport->groupBy('work_date')->keys();
        })->toArray();
        $userIds = array_keys($infoDailyReports);
        $user = $this->getAllUser(['user_ids' => $userIds])->keyBy('user_id')->toArray();
        $dailyReports = [];
        foreach ($userIds as $userId) {
            if (@$user[$userId]->contract->contract_category_id == EStaffContractType::RENTAL_CONTRACT) {
                $dailyReport = $this->dailyReportRepository->getList([
                    'user_id' => $userId,
                    'status' => [EDailyReport::SUCCESS_STATUS],
                    'work_dates' => array_values($infoDailyReports[$userId]),
                    'get_project' => 0
                ], [
                    'work_date',
                    'actual_time',
                    'title'
                ])->groupBy('work_date')->map(function ($dailyReport) use ($userId) {
                    $dailyReport = $dailyReport->toArray();
                    return [
                        'user_id' => $userId,
                        'report_date' => $dailyReport[0]['work_date'],
                        'actual_time' => array_sum(array_column($dailyReport, 'actual_time')),
                        'work_detail' => implode('\n', array_column($dailyReport, 'title'))
                    ];
                })->values()->toArray();
                $dailyReports = array_merge($dailyReports, $dailyReport);
            }
        }
        if ($dailyReports) {
            $this->updateOrCreateFreelancerTimesheets(['freelancer_timesheets' => $dailyReports]);
        }

        return $result;
    }

    public function getListForEEChart($projectId, $fromMonth, $toMonth)
    {
        $startOfFromMonth = createFromFormat($fromMonth, 'Y-m')->startOfMonth();
        $endOfToMonth = createFromFormat($toMonth, 'Y-m')->endOfMonth();
        $dailyReports = $this->dailyReportRepository->getList(
            [
                'project_id' => $projectId,
                'status' => [EDailyReport::SUCCESS_STATUS],
                'from_date' => $startOfFromMonth->format('Y-m-d'),
                'to_date' => $endOfToMonth->format('Y-m-d')
            ],
            [
                'user_id',
                'work_date',
                'actual_time',
                'project_id',
                'coefficient'
            ]
        )->toArray();
        return $dailyReports;
    }

    public function getDailyReportByUserIds($userIds, $date)
    {
        $reports = $this->dailyReportRepository->getDailyReportByUserIds($userIds, $date);
        $reports->map(function ($report) {
            return $report;
        })->filter(function ($report) {
            return $report->status == EDailyReport::SUCCESS_STATUS;
        });

        return $reports;
    }

    public function getDailyReportByProjectIds($projectIds, $date)
    {
        $reports = $this->dailyReportRepository->getDailyReportByProjectIds($projectIds, $date);
        $reports->map(function ($report) {
            return $report;
        })->filter(function ($report) {
            return $report->status == EDailyReport::SUCCESS_STATUS;
        });

        return $reports;
    }
}
