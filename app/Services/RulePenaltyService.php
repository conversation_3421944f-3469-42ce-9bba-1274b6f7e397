<?php

namespace App\Services;

use App\Repositories\RulePenaltyRepository;
use App\Repositories\RuleRepository;

class RulePenaltyService
{
    private $rulePenaltyRepository;

    private $ruleRepository;

    public function __construct(
        RulePenaltyRepository $rulePenaltyRepository,
        RuleRepository $ruleRepository
    ) {
        $this->rulePenaltyRepository = $rulePenaltyRepository;
        $this->ruleRepository = $ruleRepository;
    }

    public function getDetail()
    {
        $result = [];
        $rules = $this->ruleRepository->all();
        $rulePenalties = $this->rulePenaltyRepository->all()->groupBy('rule_id')->toArray();
        $rules->where('parent_id', null)->each(function ($item) use ($rules, $rulePenalties, &$result) {
            $item->penalties = [];
            $result[] = $item->toArray();
            $children = $rules->where('parent_id', $item->id)->map(function ($child) use ($rulePenalties) {
                $penalties = collect(@$rulePenalties[$child->id])->map(function ($penalty) {
                    return [
                        'id' => $penalty['penalty_id'],
                        'number' => $penalty['number'],
                    ]; 
                });
                $child->penalties = $penalties ?: [];

                return $child;
            })->toArray();
            $result = array_merge($result, $children);
        });

        return $result;
    }

    public function store($args)
    {
        $this->rulePenaltyRepository->deleteByColumn('rule_id', (array)$args[0]['rule_id']);

        return $this->rulePenaltyRepository->insert($args);
    }
}
