<?php

namespace App\Services;

use App\Enums\EStageType;
use App\Interfaces\StageRepositoryInterface;
use App\Repositories\UserUnpaidLeaveRepository;
use App\Traits\UserData;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Response;

class StageService
{
    use UserData;
    private $stageRepository;
    private $userUnpaidLeaveRepository;

    public function __construct(
        StageRepositoryInterface $stageRepository,
        UserUnpaidLeaveRepository $userUnpaidLeaveRepository
    ) {
        $this->stageRepository = $stageRepository;
        $this->userUnpaidLeaveRepository = $userUnpaidLeaveRepository;
    }

    public function getList($projectId)
    {
        return $this->stageRepository->getList($projectId);
    }

    public function store($args)
    {
        $stage = $this->stageRepository->store($args);

        if (empty($stage->project->start_date)) {
            $startDate = $this->stageRepository->where('project_id', $args['project_id'])->orderBy('id')->first()->start_date;
            $stage->project->start_date = $startDate;
            $stage->project->save();
        }

        return $stage;
    }

    public function update($id, $args)
    {
        $data = $this->stageRepository->update($id, $args);
        if (!$data) {
            return null;
        }

        return $data;
    }

    public function destroy($stageId)
    {
        $res = $this->stageRepository->destroy($stageId);

        return (bool) $res;
    }

    public function getDetail($stageId)
    {
        return $this->stageRepository->getDetail($stageId);
    }

    public function checkExistVersion($args, $id = null)
    {
        $versionCheck = $this->stageRepository->checkExistVersion($args, $id);
        if ($versionCheck) {
            return [
                'message' => __('stage_version_already_exist'),
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            ];
        }
    }

    public function checkResourceAllocated($args, $stageId)
    {
        $stage = $this->stageRepository->with('allocations')->find($stageId);
        if (!$stage) {
            return [
                'message' => __('not_found'),
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            ];
        }
        $endDateCheck = false;
        if ($stage->allocations->isNotEmpty()) {
            $endDateCheck = $stage->allocations->every(fn ($allocate) => $allocate->end_date > $args['end_date']);
        }
        if ($endDateCheck) {
            return [
                'message' => __('time_allocate_within_the_span_of_stage'),
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            ];
        }
    }

    public function getSchedule($projectId)
    {
        $listStage = $this->stageRepository->getList($projectId)->toArray();
        $listEvent = [];
        foreach ($listStage as $stage) {
            foreach ($stage['milestones'] as $milestone) {
                $listEvent[] = [
                    'date' => $milestone['replan_to_date'] ?? $milestone['expected_date'],
                    'event' => $milestone['name'],
                    'stage_type' => $stage['type'],
                    'stage_type_name' => EStageType::getActionString($stage['type']),
                    'version' => $stage['version'],
                ];
            }
            foreach ($stage['deliverables'] as $deliverable) {
                $listEvent[] = [
                    'date' => $deliverable['replan_to_date'] ?? $deliverable['expected_date'],
                    'event' => $deliverable['name'],
                    'stage_type' => $stage['type'],
                    'stage_type_name' => EStageType::getActionString($stage['type']),
                    'version' => $stage['version'],
                ];
            }
        }

        return [
            'event_calendar' => $listEvent,
            'all_stage' => $listStage,
        ];
    }

    public function getAllocationByStageIds($projectIds)
    {
        return $this->stageRepository->getAllocationByStageIds($projectIds);
    }

    public function checkInStageRange($stageId, $expectedDate)
    {
        $stage = $this->stageRepository->find($stageId);
        $startDate = !empty($stage->replan_start_date) ? $stage->replan_start_date : $stage->start_date;
        $endDate = !empty($stage->replan_end_date) ? $stage->replan_end_date : $stage->end_date;

        return $expectedDate >= $startDate && $expectedDate <= $endDate;
    }

    public function checkValidStageRange($stageId, $startDate, $endDate)
    {
        $stage = $this->stageRepository->find($stageId);
        $stage->load('milestones', 'deliverables');
        $milestones = $stage->milestones;
        $deliverables =  $stage->deliverables;

        foreach ($milestones as $milestone) {
            $milestoneDate = !empty($milestone->replan_to_date) ?
                $milestone->replan_to_date :
                $milestone->expected_date;

            if ($milestoneDate < $startDate || $milestoneDate > $endDate) {
                return false;
            }
        }

        foreach ($deliverables as $deliverable) {
            $deliverableDate = !empty($deliverable->replan_to_date) ?
                $deliverable->replan_to_date :
                $deliverable->expected_date;

            if ($deliverableDate < $startDate || $deliverableDate > $endDate) {
                return false;
            }
        }

        return true;
    }

    public function clone($args, $stageId)
    {
        $stage = $this->stageRepository->getDetail($stageId);
        $newStage = $this->stageRepository->store($args);
        if (!empty($stage->allocations)) {
            $userIds = $stage->allocations->pluck('user_id')->toArray();
            $users = $this->getAllUser(["user_ids" => $userIds, "fields" => ['user_id', 'level', 'checkout_date', 'onboard_date']]);
            $userUnpaidLeave = $this->userUnpaidLeaveRepository->getList(['user_id' => $userIds]);
            $userUnpaidLeave = collect($userUnpaidLeave);
            $stage->allocations->each(function ($allocation) use ($stage, $newStage, $users, $userUnpaidLeave) {
                $endStage = $stage->replan_end_date ?? $stage->end_date;
                $startStage = $stage->replan_start_date ?? $stage->start_date;
                $endNewStage = $newStage->replan_end_date ?? $newStage->end_date;
                $startNewStage = $newStage->replan_start_date ?? $newStage->start_date;
                $user = $users->firstWhere('user_id', $allocation->user_id);
                $numberOfDaysFromStart = Carbon::parse($allocation->start_date)->startOfDay()->diffInDays(
                    Carbon::parse($startStage)->startOfDay()
                );

                $startDateFromBeginOfStage = Carbon::parse($startNewStage)->startOfDay()->addDays($numberOfDaysFromStart)->format('Y-m-d');
                $startDate = isset($user->onboard_date) ? max($user->onboard_date, $startDateFromBeginOfStage) : $startDateFromBeginOfStage;
                $numberOfDaysInAllocation = Carbon::parse($allocation->end_date)->startOfDay()->diffInDays(
                    Carbon::parse($allocation->start_date)->startOfDay()
                );

                $endDateByPreviousAllocation = Carbon::parse($startDate)->startOfDay()->addDays($numberOfDaysInAllocation)->format('Y-m-d');
                $endDate = isset($user->checkout_date)
                    ? min($endDateByPreviousAllocation, $endNewStage, $user->checkout_date)
                    : min($endDateByPreviousAllocation, $endNewStage);
                if ($endStage == $allocation->end_date && Carbon::parse($stage->end_date)->isLastOfMonth() && !isset($user->checkout_date)) {
                    $endDate = Carbon::parse($endNewStage)->format('Y-m-d');
                }
                $isLeave = $userUnpaidLeave->filter(function ($leave) use ($endNewStage, $startNewStage, $allocation) {
                    return $leave->user_id == $allocation->user_id && $this->datesOverlap($leave->start_date, $leave->end_date, $startNewStage, $endNewStage) != 0;
                });
                if ($startDate <= $endDate && $isLeave->isEmpty()) {
                    $workingDays = Carbon::parse($startDate)->startOfDay()->diffInDaysFiltered(function (Carbon $date) {
                        return $date->isWeekday();
                    }, Carbon::parse($endDate)->startOfDay());

                    $newAllocate = $allocation->replicate();
                    $newAllocate->stage_id = $newStage->id;
                    $newAllocate->start_date = $startDate;
                    $newAllocate->end_date = $endDate;
                    $newAllocate->man_day = $workingDays;
                    $newAllocate->coefficient = $user->level->coefficient ?? 1;
                    $newAllocate->save();
                }
            });
        }
        return $newStage->load('allocations');
    }

    private function datesOverlap($start_one, $end_one, $start_two, $end_two)
    {

        if ($start_one <= $end_two && $end_one >= $start_two) { //If the dates overlap
            $endDate = new DateTime(min($end_one, $end_two));
            $startDate = new DateTime(max($start_two, $start_one));
            return $endDate->diff($startDate)->days + 1; //return how many days overlap
        }

        return 0; //Return 0 if there is no overlap
    }

    public function validateStageReplan($stageId, $replanStartDate, $replanEndDate): bool
    {
        if (empty($replanStartDate) || empty($replanEndDate)) {
            return false;
        }

        $stage = $this->stageRepository->find($stageId);
        $allocations = $stage->load('allocations')->allocations;

        return !$allocations->every(function ($allocation) use ($replanStartDate, $replanEndDate) {
            return $allocation->start_date >= $replanStartDate && $allocation->end_date <= $replanEndDate;
        });
    }
}
