<?php

namespace App\Services;

use App\Interfaces\PositionRepositoryInterface;

class PositionService
{
    private $positionRepository;

    public function __construct(PositionRepositoryInterface $positionRepository)
    {
        $this->positionRepository = $positionRepository;
    }

    public function getAll($args)
    {
        $position = $this->positionRepository->getAll($args);

        return $position;
    }

    public function store($args)
    {
        return $this->positionRepository->store($args);
    }

    public function update($id, $args)
    {
        return $this->positionRepository->update($id, $args);
    }

    public function destroy($id)
    {
        $res = $this->positionRepository->destroy($id);

        return (bool) $res;
    }

    public function getDetail($id)
    {
        return $this->positionRepository->find($id);
    }
}
