<?php

namespace App\Services;

use App\Interfaces\LevelRepositoryInterface;

class LevelService
{
    protected $levelRepository;

    public function __construct(LevelRepositoryInterface $levelRepository)
    {
        $this->levelRepository = $levelRepository;
    }

    public function store($args)
    {
        return $this->levelRepository->store($args);
    }

    public function getAll($args)
    {
        return $this->levelRepository->getAll($args);
    }

    public function update($id, $args)
    {
        return $this->levelRepository->update($id, $args);
    }

    public function destroy($id)
    {
        $res = $this->levelRepository->destroy($id);

        return (bool) $res;
    }
}
