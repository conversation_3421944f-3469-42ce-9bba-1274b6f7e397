<?php

namespace App\Services;

use App\Repositories\PenaltyRepository;
use App\Repositories\RulePenaltyRepository;

class PenaltyService
{
    private $penaltyRepository;

    private $rulePenaltyRepository;

    public function __construct(
        PenaltyRepository $penaltyRepository,
        RulePenaltyRepository $rulePenaltyRepository
    ) {
        $this->penaltyRepository = $penaltyRepository;
        $this->rulePenaltyRepository = $rulePenaltyRepository;
    }

    public function getAll()
    {
        return $this->penaltyRepository->all();
    }

    public function create($args)
    {
        return $this->penaltyRepository->create($args);
    }

    public function find($id, $isDeletable = false)
    {
        $args['id'] = $id;
        if ($isDeletable) $args['is_deletable'] = $isDeletable;

        return $this->penaltyRepository->firstWhere($args);
    }

    public function update($id, $args)
    {
        return $this->penaltyRepository->update($id, $args);
    }

    public function delete($id)
    {
        $this->rulePenaltyRepository->deleteByColumn('penalty_id', (array)$id);

        return $this->penaltyRepository->delete($id);
    }
}
