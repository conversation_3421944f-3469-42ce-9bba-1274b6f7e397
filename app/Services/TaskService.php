<?php

namespace App\Services;

use App\Repositories\TaskRepository;

class TaskService
{
    private $taskRepository;

    public function __construct(TaskRepository $taskRepository)
    {
        $this->taskRepository = $taskRepository;
    }

    public function getListTask($args)
    {
        return $this->taskRepository->getListTask($args);
    }

    public function store($data)
    {
        $this->taskRepository->deleteByIssueIds(array_column($data, 'issue_id'));

        return $this->taskRepository->insert($data);
    }
}
