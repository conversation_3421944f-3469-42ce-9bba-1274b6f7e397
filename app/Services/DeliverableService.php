<?php

namespace App\Services;

use App\Interfaces\DeliverableRepositoryInterface;
use App\Interfaces\StageRepositoryInterface;

class DeliverableService
{
    private $deliverableRepository;

    private $stageRepository;

    public function __construct(DeliverableRepositoryInterface $deliverableRepository, StageRepositoryInterface $stageRepository)
    {
        $this->deliverableRepository = $deliverableRepository;
        $this->stageRepository = $stageRepository;
    }

    public function store($args)
    {
        return $this->deliverableRepository->store($args);
    }

    public function getDetail($id)
    {
        return $this->deliverableRepository->getDetail($id);
    }

    public function update($id, $args)
    {
        $data = $this->deliverableRepository->update($id, $args);
        if (! $data) return null;

        return $data;
    }

    public function destroy($id)
    {
        $res = $this->deliverableRepository->destroy($id);

        return (bool) $res;
    }
}
