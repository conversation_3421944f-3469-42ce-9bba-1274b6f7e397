<?php

namespace App\Services;

use App\Interfaces\ActivityLogRepositoryInterface;

class ActivityLogService
{
    private $activityLogRepository;

    public function __construct(ActivityLogRepositoryInterface $activityLogRepository)
    {
        $this->activityLogRepository = $activityLogRepository;
    }

    public function getList($args, $id)
    {
        return $this->activityLogRepository->getList($args, $id);
    }

    public function show($id)
    {
        return $this->activityLogRepository->getDetail($id);
    }
}
