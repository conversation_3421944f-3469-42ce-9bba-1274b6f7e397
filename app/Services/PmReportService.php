<?php

namespace App\Services;

use App\Interfaces\PmReportRepositoryInterface;

class PmReportService
{
    private $pmReportRepository;

    public function __construct(PmReportRepositoryInterface $pmReportRepository)
    {
        $this->pmReportRepository = $pmReportRepository;
    }

    public function getListPmReport($args, $projectId)
    {
        $pmReport = $this->pmReportRepository->getListPmReport($args, $projectId);

        if (! empty($pmReport)) {
            $pmReportData = $pmReport
                ->getCollection()
                ->map(function ($item) {
                    if (! empty($item)) {
                        $pcv = $item->pcv->where('project_id', $item->project_id)->first();
                        $item = $item->toArray();
                        $item['pcv'] = $pcv;

                        return $item;
                    }
                });
        }
        $pmReport->setCollection($pmReportData);

        return $pmReport;
    }

    public function store($args)
    {
        return $this->pmReportRepository->store($args);
    }

    public function update($id, $args)
    {
        return $this->pmReportRepository->update($id, $args);
    }

    public function destroy($id)
    {
        $res = $this->pmReportRepository->destroy($id);

        return (bool) $res;
    }

    public function getDetail($id)
    {
        return $this->pmReportRepository->find($id);
    }

    public function getDateReported($id)
    {
        return $this->pmReportRepository
            ->select('date')
            ->where('project_id', $id)
            ->get();
    }

    public function getAllPmReports($args)
    {
        return $this->pmReportRepository->getAllPmReports($args);
    }
}
