<?php

namespace App\Services;

use App\Interfaces\ProjectUserRoleRepositoryInterface;

class ProjectUserRoleService
{
    private $projectUserRoleRepository;

    public function __construct(ProjectUserRoleRepositoryInterface $projectUserRoleRepository)
    {
        $this->projectUserRoleRepository = $projectUserRoleRepository;
    }

    public function getProjectByUserRole($args)
    {
        $projectUserRole = $this->projectUserRoleRepository->userBelongToProject($args);

        return $projectUserRole->pluck('project');
    }
}
