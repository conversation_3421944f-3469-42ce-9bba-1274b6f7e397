<?php

namespace App\Services;

use App\Interfaces\QualityGateServiceInterface;
use App\Repositories\QualityGateRepository;
use App\Repositories\ProjectRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use InvalidArgumentException;

class QualityGateService implements QualityGateServiceInterface
{
    private QualityGateRepository $qualityGateRepository;
    private ProjectRepository $projectRepository;

    public function __construct(
        QualityGateRepository $qualityGateRepository,
        ProjectRepository $projectRepository
    ) {
        $this->qualityGateRepository = $qualityGateRepository;
        $this->projectRepository = $projectRepository;
    }

    /**
     * Store project quality gate without returning the created record
     * Enforces business rule: one quality gate per project
     *
     * @param array $data
     * @return void
     * @throws ModelNotFoundException
     * @throws InvalidArgumentException
     */
    public function storeProjectQualityGate(array $data): void
    {
        // Validate required fields
        if (!isset($data['project_id'])) {
            throw new InvalidArgumentException('Project ID is required');
        }

        // Verify project exists
        $project = $this->projectRepository->find($data['project_id']);
        if (!$project) {
            throw new ModelNotFoundException('Project not found');
        }

        // Validate quality gate value if provided
        if (isset($data['quality_gate']) && !in_array($data['quality_gate'], [0, 1, 2])) {
            throw new InvalidArgumentException('Invalid quality gate value. Must be 0 (N/A), 1 (Fail), or 2 (Pass)');
        }

        // Set default values for required fields
        $qualityGateData = array_merge([
            'quality_gate' => 0, // Default to N/A
            'number_of_non_compliance' => 0,
            'number_of_process' => 0,
            'number_of_incident' => 0,
            'number_of_customer_complaint' => 0,
            'note' => null,
        ], $data);

        // Store the quality gate (will update if exists, create if not)
        // This enforces the uniqueness constraint at the application level
        $this->qualityGateRepository->storeProjectQualityGate($qualityGateData);

        // Intentionally not returning the created/updated record as per requirements
    }

    /**
     * Check if project has a quality gate
     *
     * @param int $projectId
     * @return bool
     */
    public function hasProjectQualityGate(int $projectId): bool
    {
        return $this->qualityGateRepository->hasProjectQualityGate($projectId);
    }

    /**
     * Get project quality gate
     *
     * @param int $projectId
     * @return mixed
     */
    public function getProjectQualityGate(int $projectId)
    {
        return $this->qualityGateRepository->getProjectQualityGate($projectId);
    }

    /**
     * Update project quality gate
     * This is a convenience method that uses the same store logic
     *
     * @param int $projectId
     * @param array $data
     * @return void
     */
    public function updateProjectQualityGate(int $projectId, array $data): void
    {
        $data['project_id'] = $projectId;
        $this->storeProjectQualityGate($data);
    }

    /**
     * Validate quality gate data
     *
     * @param array $data
     * @return array
     */
    private function validateQualityGateData(array $data): array
    {
        $errors = [];

        if (!isset($data['project_id']) || !is_numeric($data['project_id'])) {
            $errors[] = 'Valid project ID is required';
        }

        if (isset($data['quality_gate']) && !in_array($data['quality_gate'], [0, 1, 2])) {
            $errors[] = 'Quality gate must be 0 (N/A), 1 (Fail), or 2 (Pass)';
        }

        if (isset($data['number_of_non_compliance']) && (!is_numeric($data['number_of_non_compliance']) || $data['number_of_non_compliance'] < 0)) {
            $errors[] = 'Number of non-compliance must be a non-negative integer';
        }

        if (isset($data['number_of_process']) && (!is_numeric($data['number_of_process']) || $data['number_of_process'] < 0)) {
            $errors[] = 'Number of process must be a non-negative integer';
        }

        if (isset($data['number_of_incident']) && (!is_numeric($data['number_of_incident']) || $data['number_of_incident'] < 0)) {
            $errors[] = 'Number of incident must be a non-negative integer';
        }

        if (isset($data['number_of_customer_complaint']) && (!is_numeric($data['number_of_customer_complaint']) || $data['number_of_customer_complaint'] < 0)) {
            $errors[] = 'Number of customer complaint must be a non-negative integer';
        }

        if (!empty($errors)) {
            throw new InvalidArgumentException('Validation failed: ' . implode(', ', $errors));
        }

        return $data;
    }
}
