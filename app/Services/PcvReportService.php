<?php

namespace App\Services;

use App\Enums\EEvaluateStatus;
use App\Interfaces\PcvReportRepositoryInterface;
use App\Repositories\PmReportRepository;

class PcvReportService
{
    private $pcvReportRepository;
    private $pmReportRepository;

    public function __construct(
        PcvReportRepositoryInterface $pcvReportRepository,
        PmReportRepository $pmReportRepository
    )
    {
        $this->pcvReportRepository = $pcvReportRepository;
        $this->pmReportRepository = $pmReportRepository;
    }

    public function getPcvReports($projectId)
    {
        return $this->pcvReportRepository->getPcvReports($projectId);
    }

    public function processCasePmReport($args)
    {
        $projectId = $args['project_id'];
        $countPmReportedByWeek = $this->pmReportRepository->countPmReportedByWeek($args['date'], $projectId);

        if ($countPmReportedByWeek == 0) {
            $this->pmReportRepository->store([
                'date' => $args['date'],
                'user_id' => $args['user_id'],
                'project_id' => $projectId,
                'cost_status' =>  EEvaluateStatus::NA,
                'quality_status' => EEvaluateStatus::NA,
                'timeliness_status' => EEvaluateStatus::NA,
                'customer_feedback_status' => EEvaluateStatus::NA,
                'process_status' => EEvaluateStatus::NA
            ]);
        }
    }

    public function store($args, $projectId)
    {
        $args['project_id'] = $projectId;
        $this->processCasePmReport($args);

        $pcvReport = $this->pcvReportRepository->store($args);

        return $pcvReport;
    }

    public function update($reportId, $args)
    {
        $pcvReport = $this->pcvReportRepository->update($reportId, $args);

        return $pcvReport;
    }

    public function destroy($id)
    {
        $res = $this->pcvReportRepository->destroy($id);

        return (bool) $res;
    }
}
