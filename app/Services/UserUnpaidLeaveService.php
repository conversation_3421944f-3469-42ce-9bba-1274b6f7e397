<?php

namespace App\Services;

use App\Repositories\AllocationRepository;
use App\Repositories\DailyReportRepository;
use App\Repositories\UserUnpaidLeaveRepository;

class UserUnpaidLeaveService
{
    protected UserUnpaidLeaveRepository $userUnpaidLeaveRepository;
    protected DailyReportRepository $dailyReportRepository;
    protected AllocationRepository $allocationRepository;

    public function __construct(
        UserUnpaidLeaveRepository $userUnpaidLeaveRepository,
        AllocationRepository $allocationRepository,
        DailyReportRepository $dailyReportRepository
    ) {
        $this->userUnpaidLeaveRepository = $userUnpaidLeaveRepository;
        $this->allocationRepository = $allocationRepository;
        $this->dailyReportRepository = $dailyReportRepository;
    }

    public function getList($args)
    {
        return $this->userUnpaidLeaveRepository->getList($args);
    }

    public function store($args)
    {
        return $this->userUnpaidLeaveRepository->create($args);
    }

    public function update($id, $args)
    {
        return $this->userUnpaidLeaveRepository->update($id, $args);
    }

    public function destroy($id)
    {
        $res = $this->userUnpaidLeaveRepository->destroy($id);

        return (bool) $res;
    }

    public function get($args)
    {
        return $this->userUnpaidLeaveRepository->get($args);
    }

    public function checkAllocationValid($userIds, $dates)
    {
        return $this->allocationRepository
            ->getAllocationByUserIds($userIds, $dates)
            ->count();
    }

    public function checkDailyReportDuplicate($userId, $fromDate, $toDate) : bool
    {
        return (bool)$this->dailyReportRepository->getList([
            'user_id' => $userId,
            'from_date' => $fromDate,
            'to_date' => $toDate
        ])->count();
    }
}
