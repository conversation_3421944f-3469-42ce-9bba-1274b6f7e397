<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileService
{
    public function uploadFileS3($file, $filePath)
    {
        $fileName = $file->getClientOriginalName();
        $fileName = preg_replace('/\s+/', '', $fileName);
        $randomStr = Str::random(10);
        $filePath = $filePath;
        $url = $randomStr.'-'.$fileName;

        return $file->storeAs($filePath, $url, 's3');
    }

    public function deleteFileS3($fileUrl)
    {
        $filePath = parse_url($fileUrl)['path'];
        Storage::disk('s3')->delete($filePath);
    }

    public function deleteMultipleFileS3($files)
    {
        foreach ($files as $file) {
            Storage::disk('s3')->delete($file);
        }
    }
}
