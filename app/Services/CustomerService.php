<?php

namespace App\Services;

use App\Interfaces\CustomerRepositoryInterface;

class CustomerService
{
    private $customerRepository;

    public function __construct(CustomerRepositoryInterface $customerRepository)
    {
        $this->customerRepository = $customerRepository;
    }

    public function getCustomers($fields, $keyword, $customerId)
    {
        return $this->customerRepository->getCustomers($fields, $keyword, $customerId);
    }

    public function getListCustomer($args = arr)
    {
        $customers = $this->customerRepository->getListCustomer($args);

        return $customers;
    }

    public function store($args)
    {
        $customer = $this->customerRepository->store($args);
        $customerId = $customer->id;
        $customerCode = 'KH'.$customerId;
        $customer->code = $customerCode;
        $customer->save();
        $customer->projects()->sync($args['project_ids'] ?? []);

        return $customer;
    }

    public function update($id, $args)
    {
        $customer = $this->customerRepository->update($id, $args);
        if (! $customer) return null;
        if (array_key_exists('project_ids', $args)) {
            $customer->projects()->sync($args['project_ids'] ?? []);
        }

        return $customer;
    }

    public function destroy($id)
    {
        $res = $this->customerRepository->destroy($id);

        return (bool) $res;
    }
    
    public function getDivisionCustomers($divisionId)
    {
        return $this->customerRepository->getDivisionCustomers($divisionId);
    }
}
