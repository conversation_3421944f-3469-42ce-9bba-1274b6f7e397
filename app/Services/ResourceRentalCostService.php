<?php

namespace App\Services;

use App\Repositories\ResourceRentalCostRepository;
use App\Repositories\ResourceRentalMonthCostRepository;
use App\Traits\Helper;

class ResourceRentalCostService
{
    use Helper;

    private ResourceRentalCostRepository $resourceRentalCostRepository;
    private ResourceRentalMonthCostRepository $resourceRentalMonthCostRepository;

    public function __construct(
        ResourceRentalCostRepository      $resourceRentalCostRepository,
        ResourceRentalMonthCostRepository $resourceRentalMonthCostRepository
    )
    {
        $this->resourceRentalCostRepository = $resourceRentalCostRepository;
        $this->resourceRentalMonthCostRepository = $resourceRentalMonthCostRepository;
    }

    public function getList($input)
    {
        $relations = $input['relations'] ?? ['monthCosts'];

        return $this->resourceRentalCostRepository->getWhere($input, ['*'], $relations);
    }

    public function updateOrCreate($input)
    {
        $resourceRentalCost = $this->resourceRentalCostRepository->updateOrCreate(
            ['id' => @$input['id']],
            [
                'project_id' => (int)$input['project_id'],
                'division_id' => $input['division_id'],
                'year' => $input['year'],
                'note' => $input['note'] ?? null,
            ]
        );

        if ($resourceRentalCost) {
            $this->resourceRentalMonthCostRepository->deleteWhere(
                [
                    'resource_rental_cost_id' => $resourceRentalCost->id,
                ]
            );

            $resourceRentalCosts = $this->getRentalMonthCostParams($resourceRentalCost->id, $input['month_costs'] ?? []);

            if ($resourceRentalCosts) {
                $this->resourceRentalMonthCostRepository->insert($resourceRentalCosts);
            }
        }

        return $resourceRentalCost;
    }

    private function getRentalMonthCostParams($rentalCostId, $monthCosts)
    {
        return collect($monthCosts)
            ->map(fn($value) => [
                'resource_rental_cost_id' => $rentalCostId,
                'month' => $value['month'],
                'cost' => $value['cost'] ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ])
            ->filter(fn($value) => !empty($value['month']) && !empty($value['cost']))
            ->values()
            ->toArray();
    }

    public function validateYearDuplicate($input): bool
    {
        $resourceRentalCost = $this->resourceRentalCostRepository->getWhere(
            [
                'project_ids' => (int)$input['project_id'],
                'division_ids' => $input['division_id'],
                'year' => $input['year'],
                'ignore_ids' => $input['id'] ?? null
            ]
        )->first();

        return (bool)$resourceRentalCost;
    }

    public function delete($id)
    {
        $resourceRentalCost = $this->resourceRentalCostRepository->find($id);

        if ($resourceRentalCost) {
            $this->resourceRentalMonthCostRepository->deleteWhere(
                [
                    'resource_rental_cost_id' => $resourceRentalCost->id,
                ]
            );

            return (bool)$resourceRentalCost->delete();
        }

        return false;
    }
}
