<?php

namespace App\Services;

use App\Enums\ECommon;
use App\Enums\EProjectType;
use App\Repositories\AllocationRepository;
use App\Repositories\ProjectMonthBudgetRepository;
use App\Repositories\ResourceRentalCostRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ChargeRateService
{
    private AllocationRepository $allocationRepository;
    private ProjectMonthBudgetRepository $projectMonthBudgetRepository;
    private ResourceRentalCostRepository $resourceRentalCostRepository;

    public function __construct(
        AllocationRepository $allocationRepository,
        ProjectMonthBudgetRepository $projectMonthBudgetRepository,
        ResourceRentalCostRepository $resourceRentalCostRepository
    ) {
        $this->allocationRepository = $allocationRepository;
        $this->projectMonthBudgetRepository = $projectMonthBudgetRepository;
        $this->resourceRentalCostRepository = $resourceRentalCostRepository;
    }

    public function getChargeRateStatistics($args = [])
    {
        $projectsTypes = [EProjectType::BY_CUSTOMER, EProjectType::OPPORTUNITY, EProjectType::TRAINING];

        $args['project_types'] = $projectsTypes;
        $projectBudgets = $this->projectMonthBudgetRepository->getProjectBudgets($args);

        $rentalCosts = $this->resourceRentalCostRepository->getWhere(
            [
                'year' => $args['year'],
                'project_types' => $projectsTypes,
            ],
            ['*'],
            ['project:id,name,division_id,project_type', 'monthCosts']
        );
        foreach ($rentalCosts as $rentalCost) {
            foreach ($rentalCost->monthCosts as $monthCost) {
                $month = Carbon::parse($rentalCost->year . '-' . $monthCost->month)->format('Y-m');
                $monthCost->setMonth($month);
            }
        }
        return [
            'project_budgets' => $projectBudgets,
            'rental_costs' => $rentalCosts,
        ];
    }

    public function getMonthBudget($args)
    {
        $divisionId = $args['division_id'];
        $year = $args['year'];
        $projectsTypes = [EProjectType::BY_CUSTOMER, EProjectType::OPPORTUNITY, EProjectType::TRAINING];

        $budgetArgs = [
            'division_ids' => [$divisionId],
            'project_types' => $projectsTypes,
            'year' => $year
        ];
        $budgetRecords = $this->projectMonthBudgetRepository->getProjectBudgets($budgetArgs);
        $budgets = $budgetRecords->map(function ($record) {
            return [
                'month' => $record['month'],
                'project_id' => $record['project_id'],
                'team_id' => $record['project']['team_id'],
                'budget' => $record['budget']
            ];
        });

        $months = generateMonthsInYear($year);

        $divisionBudgetByMonth = $budgets->groupBy('month')->map(function ($monthRecords) {
            return collect($monthRecords)->sum('budget');
        });

        $divisionBudget = collect($months)->mapWithKeys(function ($month) use ($divisionBudgetByMonth) {
            return [$month => $divisionBudgetByMonth[$month] ?? 0];
        });

        $teamBudget = $budgets->groupBy('team_id')->map(function ($teamRecords) use ($months) {
            $teamBudgetByMonth = collect($teamRecords)->groupBy('month')->map(function ($teamMonthRecords) {
                return collect($teamMonthRecords)->sum('budget');
            });
            return collect($months)->mapWithKeys(function ($month) use ($teamBudgetByMonth) {
                return [$month => $teamBudgetByMonth[$month] ?? 0];
            });
        });

        return [
            'division_budget' => $divisionBudget,
            'team_budget' => $teamBudget
        ];
    }

    public function getDivisionMonthBudgets($args)
    {
        $year = $args['year'];
        $months = generateMonthsInYear($year);
        $projectsTypes = [EProjectType::BY_CUSTOMER, EProjectType::OPPORTUNITY, EProjectType::TRAINING];
        $args['project_types'] = $projectsTypes;
        $budgetRecords = $this->projectMonthBudgetRepository->getProjectBudgets($args);
        Log::channel(ECommon::LOG_CHANNEL_API)->info('[Charge rate] $budgetRecords:', $budgetRecords->toArray());

        return $budgetRecords->groupBy(fn($item) => $item->project->{$args['id_key']})
            ->map(function ($item) {
                return $item->groupBy('month')->map(function ($monthRecords) {
                    return collect($monthRecords)->sum('budget');
                });
            })
            ->map(function ($item) use ($months) {
                return collect($months)->mapWithKeys(function ($month) use ($item) {
                    return [$month => $item[$month] ?? 0];
                });
            });
    }
}
