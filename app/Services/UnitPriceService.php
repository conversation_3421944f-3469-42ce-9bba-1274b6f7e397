<?php

namespace App\Services;

use App\Repositories\UnitPriceRepository;

class UnitPriceService
{
    private UnitPriceRepository $unitPriceRepository;

    public function __construct(UnitPriceRepository $unitPriceRepository)
    {
        $this->unitPriceRepository = $unitPriceRepository;
    }

    public function insert($customerId, $divisionId, $unitPrices)
    {
        $unitPrices = array_map(function ($unitPrice) use ($customerId, $divisionId) {
            $unitPrice['customer_id'] = $customerId;
            $unitPrice['division_id'] = $divisionId;

            return $unitPrice;
        }, $unitPrices);

        return $this->unitPriceRepository->insert($unitPrices);
    }

    public function updateOrInsert($customerId, $divisionId, $unitPrices)
    {
        $this->unitPriceRepository->deleteWhere([
            'customer_id' => $customerId,
            'division_id' => $divisionId
        ]);

        $this->insert($customerId, $divisionId, $unitPrices);
    }

    public function deleteByCustomerId($customerId)
    {
        $this->unitPriceRepository->deleteWhere([
            'customer_id' => $customerId
        ]);
    }
}
