<?php

namespace App\Services;

use App\Enums\ERule;
use App\Repositories\RuleRepository;
use App\Repositories\RulePenaltyRepository;

class RuleService
{
    private $ruleRepository;

    private $rulePenaltyRepository;

    public function __construct(
        RuleRepository $ruleRepository,
        RulePenaltyRepository $rulePenaltyRepository
    ) {
        $this->ruleRepository = $ruleRepository;
        $this->rulePenaltyRepository = $rulePenaltyRepository;
    }

    public function getAll()
    {
        return $this->ruleRepository->all();
    }

    public function create($args)
    {
        $result = [];
        if (@$args['parent_id']) {
            $id = $args['parent_id'];
            $rule = $this->ruleRepository->find($id);
            if (!$rule) return null;
        } elseif (@$args['name']) {
            $result = $this->ruleRepository->create(['name' => $args['name']]);
            $id = $result->id;
        }
        if (@$args['children'] && @$id) {
            $rules = [];
            foreach ($args['children'] as $value) {
                $rules[] = [
                    'name' => $value['name'],
                    'parent_id' => $id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            $result = $this->ruleRepository->insert($rules);
        }

        return $result;
    }

    public function find($id, $isDeletable = false)
    {
        $args['id'] = $id;
        if ($isDeletable) $args['is_deletable'] = $isDeletable;

        return $this->ruleRepository->firstWhere($args);
    }

    public function update($id, $args)
    {
        return $this->ruleRepository->update($id, $args);
    }

    public function delete($id)
    {
        $result = $this->find($id, ERule::IS_DELETABLE);
        if (!$result) return null;

        return $result->deleteWithChildren();
    }
}
