<?php

namespace App\Services;

use App\Enums\ERole;
use App\Interfaces\RoleRepositoryInterface;

class RoleService
{
    private $roleRepository;

    public function __construct(RoleRepositoryInterface $roleRepository)
    {
        $this->roleRepository = $roleRepository;
    }

    public function getRoles($args = [])
    {
        return $this->roleRepository->getRoles($args);
    }

    public function update($id, $args)
    {
        return $this->roleRepository->update($id, $args);
    }
}
