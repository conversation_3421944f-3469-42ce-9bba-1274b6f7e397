<?php

namespace App\Services;

use App\Interfaces\PermissionRoleRepositoryInterface;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;

class PermissionRoleService
{
    protected $permissionRoleRepository;

    protected $projectService;

    public function __construct(
        PermissionRoleRepositoryInterface $permissionRoleRepository,
        ProjectService $projectService
    ) {
        $this->permissionRoleRepository = $permissionRoleRepository;
        $this->projectService = $projectService;
    }

    public function syncDataPermission()
    {
        $positionId = request()->position_id;
        $userId = request()->user_id;
        $response = Http::put(
            env('LINK_USER_SERVICE').'sso/user/update/'.$userId,
            [
                'position_id' => $positionId,
            ]
        )->json();

        if (@$response['status'] == Response::HTTP_OK) {
            $rolePermission = $this->permissionRoleRepository
                ->getList([
                    'is_position' => 1,
                    'position_id' => $positionId,
                ])
                ->groupBy('position_id')
                ->map(function ($item) {
                    return $item->pluck('permission_name');
                })
                ->toArray();

            Redis::set($userId, json_encode($rolePermission[$positionId]));
            $this->projectService->syncPermissionToRedis((array) $userId);
        }
    }
}
