<?php

namespace App\Services;

use App\Repositories\WeeklyReportRepository;

class WeeklyReportService
{
    private $weeklyReportRepository;

    public function __construct(
        WeeklyReportRepository $weeklyReportRepository
    ) {
        $this->weeklyReportRepository = $weeklyReportRepository;
    }

    public function getAll($args)
    {
        return $this->weeklyReportRepository->getAll($args);
    }

    public function create($args)
    {
        return $this->weeklyReportRepository->create($args);
    }

    public function update($id, $args)
    {
        return $this->weeklyReportRepository->update($id, $args);
    }

    public function destroy($id)
    {
        return (bool) $this->weeklyReportRepository->destroy($id);
    }
}
