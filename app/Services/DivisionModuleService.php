<?php

namespace App\Services;

use App\Interfaces\AllocationRepositoryInterface;
use App\Repositories\DailyReportRepository;

class DivisionModuleService
{
    protected $allocationRepository;
    protected $dailyReportRepository;

    public function __construct(
        AllocationRepositoryInterface $allocationRepository,
        DailyReportRepository $dailyReportRepository
    ){
        $this->allocationRepository = $allocationRepository;
        $this->dailyReportRepository = $dailyReportRepository;
    }

    public function getDailyReportsAndAllocations($args)
    {
        $reportArgs = [
            'user_ids' => $args['user_ids'],
            'from_date' => $args['from_date'],
            'to_date' => $args['to_date'],
            'status' => $args['daily_report_status'],
            'columns' => $args['daily_report_columns']
        ];
        $dailyReportColumns = $args['daily_report_columns'] ?? ['*'];
        $dailyReports = $this->dailyReportRepository->getList($args, $dailyReportColumns);
        
       
        $allocationArgs = [
            'user_ids' => $args['user_ids'],
            'from_date' => $args['from_date'],
            'to_date' => $args['to_date'],
        ];
        $allocations = $this->allocationRepository->getRawAllocations($args);
        
        return [
            'daily_reports' => $dailyReports,
            'allocations' => $allocations
        ];
    }
}
