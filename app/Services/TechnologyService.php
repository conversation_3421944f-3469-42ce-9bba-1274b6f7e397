<?php

namespace App\Services;

use App\Interfaces\TechnologyRepositoryInterface;

class TechnologyService
{
    private $technologyRepository;

    public function __construct(TechnologyRepositoryInterface $technologyRepository)
    {
        $this->technologyRepository = $technologyRepository;
    }

    public function getTechnologies($keyword = '')
    {
        return $this->technologyRepository->getTechnologies($keyword);
    }
}
