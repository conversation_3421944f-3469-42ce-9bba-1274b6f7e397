<?php

namespace App\Services;

use App\Enums\EActionLog;
use App\Enums\ERule;
use App\Enums\EPenalty;
use App\Enums\EViolationReport;
use App\Jobs\ActivityLogChangeViolationReport;
use App\Repositories\ViolationReportPenaltyRepository;
use App\Repositories\ViolationReportRepository;
use App\Repositories\RulePenaltyRepository;
use App\Repositories\PenaltyRepository;
use App\Repositories\RuleRepository;

class ViolationReportService
{
    private $violationReportRepository;

    private $violationReportPenaltyRepository;

    private $rulePenaltyRepository;

    private $penaltyRepository;

    private $ruleRepository;

    private $fileService;

    public function __construct(
        ViolationReportRepository $violationReportRepository,
        ViolationReportPenaltyRepository $violationReportPenaltyRepository,
        RulePenaltyRepository $rulePenaltyRepository,
        PenaltyRepository $penaltyRepository,
        RuleRepository $ruleRepository,
        FileService $fileService
    ) {
        $this->violationReportRepository = $violationReportRepository;
        $this->violationReportPenaltyRepository = $violationReportPenaltyRepository;
        $this->rulePenaltyRepository = $rulePenaltyRepository;
        $this->penaltyRepository = $penaltyRepository;
        $this->ruleRepository = $ruleRepository;
        $this->fileService = $fileService;
    }

    public function getListViolationReport($args)
    {
        return $this->violationReportRepository->getListViolationReport($args);
    }

    public function store($args)
    {
        $violationReport = $this->violationReportRepository->create($args);
        dispatch(new ActivityLogChangeViolationReport($violationReport['creator_id'], $violationReport, 'violation_report', EActionLog::VIOLATION_REPORT_ADDED))
            ->delay(now()->addSecond())
            ->afterCommit();

        return $violationReport;
    }

    public function update($id, $args)
    {
        $urls = [];
        $result = $this->violationReportRepository->find($id);
        $statusProcessed = EViolationReport::PROCESSED;
        if (!$result || ($result['status'] == $statusProcessed && @$args['status'] != $statusProcessed)) return null;
        if ($result['status'] != $statusProcessed && @$args['status'] == $statusProcessed) {
            $ruleId = $args['rule_id'];
            $rule = $this->ruleRepository->update($ruleId, ['is_deletable' => ERule::NON_DELETABLE]);
            if (@$rule['parent_id']) {
                $this->ruleRepository->update($rule['parent_id'], ['is_deletable' => ERule::NON_DELETABLE]);
            }
            $countViolations = $this->violationReportRepository->countWhere([
                'violator_id' => $args['violator_id'],
                'rule_id' => $ruleId,
                'status' => $statusProcessed,
            ]);
            $penalties = $this->rulePenaltyRepository->getPenalties($ruleId, ++$countViolations)->toArray();
            if ($penalties) $args['result'] = implode(', ', array_column($penalties, 'name'));
            $penaltyId = array_column($penalties, 'id');
            $violationReportPenalties = [];
            foreach ($penalties as $penalty) {
                $violationReportPenalties[] = [
                    'violation_report_id' => $id,
                    'penalty_id' => $penalty['id'],
                ];
            }
            if ($violationReportPenalties) {
                $this->violationReportPenaltyRepository->insert($violationReportPenalties);
                $this->penaltyRepository->multiUpdate('id', $penaltyId, ['is_deletable' => EPenalty::NON_DELETABLE]);
            }
        }
        if (@$args['evidence_url'] && $evidenceUrl = $result->getRawOriginal('evidence_url')) $urls[] = $evidenceUrl;
        if (@$args['result_url'] && $resultUrl = $result->getRawOriginal('result_url')) $urls[] = $resultUrl;
        if ($urls) $this->fileService->deleteMultipleFileS3($urls);
        $violationReport = $this->violationReportRepository->update($id, $args);
        dispatch(new ActivityLogChangeViolationReport(
            $args['user_id'],
            $violationReport,
            'violation_report',
            EActionLog::VIOLATION_REPORT_UPDATED,
            $result->getAttributes()
        ))->delay(now()->addSecond())->afterCommit();

        return $violationReport;
    }

    public function delete($id, $args)
    {
        $urls = [];
        $result = $this->violationReportRepository->find($id);
        if (!$result) return null;
        if ($evidenceUrl = $result->getRawOriginal('evidence_url')) $urls[] = $evidenceUrl;
        if ($resultUrl = $result->getRawOriginal('result_url')) $urls[] = $resultUrl;
        if ($urls) $this->fileService->deleteMultipleFileS3($urls);
        $this->violationReportPenaltyRepository->deleteWhere(['violation_report_id' => $id]);
        $violationReport = $this->violationReportRepository->delete($id);
        ActivityLogChangeViolationReport::dispatchSync(
            $args['user_id'],
            $result,
            'violation_report',
            EActionLog::VIOLATION_REPORT_DELETED
        );

        return $violationReport;
    }
}
