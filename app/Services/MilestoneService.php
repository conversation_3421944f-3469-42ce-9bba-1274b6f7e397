<?php

namespace App\Services;

use App\Interfaces\MilestoneRepositoryInterface;
use App\Interfaces\StageRepositoryInterface;

class MilestoneService
{
    private MilestoneRepositoryInterface $milestoneRepository;

    private StageRepositoryInterface $stageRepository;

    public function __construct(MilestoneRepositoryInterface $milestoneRepository, StageRepositoryInterface $stageRepository)
    {
        $this->milestoneRepository = $milestoneRepository;
        $this->stageRepository = $stageRepository;
    }

    public function store($args)
    {
        return $this->milestoneRepository->store($args);
    }

    public function getDetail($id)
    {
        return $this->milestoneRepository->getDetail($id);
    }

    public function update($id, $args)
    {
        $data = $this->milestoneRepository->update($id, $args);
        if (! $data) return null;

        return $data;
    }

    public function destroy($id)
    {
        $res = $this->milestoneRepository->destroy($id);

        return (bool) $res;
    }
}
