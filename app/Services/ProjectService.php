<?php

namespace App\Services;

use App\Enums\EDailyReport;
use App\Enums\Enum;
use App\Enums\EPosition;
use App\Enums\EProjectStatus;
use App\Enums\EProjectType;
use App\Enums\ERankType;
use App\Enums\ERole;
use App\Interfaces\ProjectMonthBudgetRepositoryInterface;
use App\Interfaces\ProjectRepositoryInterface;
use App\Interfaces\ProjectStakeholderRepositoryInterface;
use App\Interfaces\ProjectUserRoleRepositoryInterface;
use App\Interfaces\RoleRepositoryInterface;
use App\Repositories\AllocationRepository;
use App\Repositories\DailyReportRepository;
use App\Repositories\DefectRepository;
use App\Repositories\FunctionRepository;
use App\Repositories\ProjectUserRoleRepository;
use App\Repositories\QualityGateRepository;
use App\Repositories\Repository;
use App\Repositories\RoleRepository;
use App\Repositories\SprintRepository;
use App\Repositories\StageRepository;
use App\Traits\Helper;
use App\Traits\UserData;
use Carbon\Carbon;
use DateInterval;
use DateTime;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Redis;

class ProjectService
{
    use Helper, UserData;

    /**
     * @var projectUserRoleRepository  App\Repositories\ProjectUserRoleRepository
     */
    private $projectRepository;

    /**
     * @var ProjectUserRoleRepository
     */
    private $projectUserRoleRepository;

    private $projectStakeholderRepository;

    /**
     * @var RoleRepository
     */
    private $roleRepository;

    /**
     * @var AllocationRepository
     */
    private $allocationRepository;

    /**
     * @var DailyReportRepository
     */
    private $dailyReportRepository;

    /**
     * @var ProjectMonthBudgetRepository
     */
    private $projectMonthBudgetRepository;

    public function __construct(
        ProjectRepositoryInterface $projectRepository,
        ProjectUserRoleRepositoryInterface $projectUserRoleRepository,
        ProjectStakeholderRepositoryInterface $projectStakeholderRepository,
        RoleRepositoryInterface $roleRepository,
        AllocationRepository $allocationRepository,
        DailyReportRepository $dailyReportRepository,
        ProjectMonthBudgetRepositoryInterface $projectMonthBudgetRepository

    ) {
        $this->projectRepository = $projectRepository;
        $this->projectUserRoleRepository = $projectUserRoleRepository;
        $this->projectStakeholderRepository = $projectStakeholderRepository;
        $this->roleRepository = $roleRepository;
        $this->allocationRepository = $allocationRepository;
        $this->dailyReportRepository = $dailyReportRepository;
        $this->projectMonthBudgetRepository = $projectMonthBudgetRepository;
    }

    public function store($args)
    {
        $args['rank'] = $this->setRankAttribute($args['billable']);
        $project = $this->projectRepository->store($args);
        $projectId = $project->id;
        $projectCode = 'PJ' . sprintf('%05d', $projectId - 1);
        $project->code = $projectCode;
        $project->save();
        $project->customers()->sync($args['customer_ids'] ?? []);

        $roles = $this->roleRepository->get();
        $members['project_id'] = $projectId;
        if (array_key_exists('pm_ids', $args)) {
            $membersRole = $roles->find(ERole::PM);
            $members['user_ids'] = $args['pm_ids'];
            $members['role_id'] = $membersRole->id;
            $this->projectUserRoleRepository->addMember($members);
            $this->syncPermissionToRedis($args['pm_ids']);
        }
        if (array_key_exists('pqa_ids', $args)) {
            $membersRole = $roles->find(ERole::PQA);
            $members['user_ids'] = $args['pqa_ids'];
            $members['role_id'] = $membersRole->id;
            $this->projectUserRoleRepository->addMember($members);
        }
        if (array_key_exists('seller_ids', $args)) {
            $membersRole = $roles->find(ERole::SALES);
            $members['user_ids'] = $args['seller_ids'];
            $members['role_id'] = $membersRole->id;
            $this->projectUserRoleRepository->addMember($members);
        }
        if (array_key_exists('stakeholder_ids', $args)) {
            $this->projectStakeholderRepository->addStakeholder($projectId, $args['stakeholder_ids']);
        }

        return $project;
    }

    public function setRankAttribute($billable)
    {
        if ($billable >= 50) {
            $rank = ERankType::RANK_A;
        } elseif ($billable < 50 && $billable > 30) {
            $rank = ERankType::RANK_B;
        } elseif ($billable >= 5) {
            $rank = ERankType::RANK_D;
        } else {
            $rank = ERankType::RANK_C;
        }
        return $rank;
    }

    public function addIndexToProjects($args, $projects)
    {
        if (@$args['page'] || @$args['limit']) {
            $page = $args['page'] ?? Config::get('define.page_default');
            $pageLength = $args['limit'] ?? Config::get('define.projectPagination');
            $pageStart = ($page - 1) * $pageLength;

            $projects->map(function ($project, $index) use ($pageStart) {
                $project->index = ++$index + $pageStart;

                return $project;
            });
        }
    }

    public function getListProject($args = null)
    {
        $args['not_status'] = [
            EProjectStatus::CANCELED,
            EProjectStatus::CLOSED,
        ];
        if (isset($args['status'])) {
            unset($args['not_status']);
        }

        $projects = $this->projectRepository->getListProject($args);
        $this->addIndexToProjects($args, $projects);

        if (!isset($args['fields'])) {
            $projects->map(function ($project) {
                $project->pm_ids = $project->pms->pluck('user_id')->toArray();
                $project->pqa_ids = $project->pqas->pluck('user_id')->toArray();
                $project->seller_ids = $project->sellers->pluck('user_id')->toArray();
                unset($project->pms, $project->pqas, $project->sellers);

                return $project;
            });
        }

        return $projects;
    }

    public function find($id)
    {
        return $this->projectRepository->find($id);
    }

    public function addMember($args)
    {
        $pivot = $this->projectUserRoleRepository->addMember($args);
        $this->syncPermissionToRedis($args['user_ids']);

        return $pivot;
    }

    public function isUniqueMember($args)
    {
        $userIds = $args['user_ids'];
        $roleId = $args['role_id'];
        $projectId = $args['project_id'];

        return collect($userIds)->every(function ($userId) use ($roleId, $projectId) {
            return $this->projectUserRoleRepository->countWhere([
                'user_id' => $userId,
                'role_id' => $roleId,
                'project_id' => $projectId
            ]) == 0;
        });
    }

    public function addMemberWhenAddAllocation($args, $projectId)
    {
        $this->projectUserRoleRepository->addMemberWhenAddAllocation($args, $projectId);
        $this->syncPermissionToRedis($args['user_ids']);
    }

    public function syncPermissionToRedis($userIds)
    {
        $permissions = $this->projectUserRoleRepository
            ->getProjectUserRole([
                'user_ids' => $userIds,
            ])
            ->groupBy('user_id')
            ->map(function ($item) {
                return $item
                    ->groupBy('project_id')
                    ->map(function ($item) {
                        return $item->pluck('permission_name');
                    });
            })
            ->toArray();

        foreach ($userIds as $userId)
            Redis::del(Enum::IN_PROJECT . $userId);

        foreach ($permissions as $userId => $permission)
            Redis::set(Enum::IN_PROJECT . $userId, json_encode($permission));
    }

    public function removeMember($id)
    {
        $memberId = $this->projectUserRoleRepository->find($id)->user_id;
        $res = $this->projectUserRoleRepository->removeMember($id);
        $this->syncPermissionToRedis([$memberId]);

        return $memberId;
    }

    public function checkExistAllocationMember($id)
    {
        $member = $this->projectUserRoleRepository->find($id);

        return $this->allocationRepository->checkExistAllocationMember($member->user_id, $member->project_id);
    }

    public function show($id, $args)
    {
        $project = $this->projectRepository->find($id);
        if ($project) {
            $pivot = $this->projectUserRoleRepository->getPivotData($project->id, $args)->toArray();
            $project->user_role = $pivot;
        }

        return $project;
    }

    public function getMemberIdsByRoles($id, $role_names)
    {
        $project = $this->projectRepository->find($id);
        $role_names = array_map(function ($item) {
            return trim($item);
        }, $role_names);
        $role_names = array_filter($role_names, function ($item) {
            return !empty($item);
        });

        if ($project) {
            $role_ids = $this->roleRepository->getByRoleNames($role_names);
            $pivot = $this->projectUserRoleRepository->getUsersByRoles($id, $role_ids)->get();

            return $pivot->pluck('user_id');
        }

        return [];
    }

    public function update($id, $args)
    {
        $args['rank'] = $this->setRankAttribute($args['billable']);
        $project = $this->projectRepository->update($id, $args);
        $roles = $this->roleRepository->get();
        if (!$project) {
            return null;
        }
        if (array_key_exists('customer_ids', $args)) {
            $project->customers()->sync($args['customer_ids']);
        }
        if (array_key_exists('technology_ids', $args)) {
            $project->technologies()->sync($args['technology_ids']);
        }

        $members['project_id'] = $id;
        if (array_key_exists('pm_ids', $args)) {
            $this->projectUserRoleRepository->destroyWithRole($id, ERole::PM);
            $membersRole = $roles->find(ERole::PM);
            $members['user_ids'] = $args['pm_ids'];
            $members['role_id'] = $membersRole->id;
            $this->projectUserRoleRepository->addMember($members);
            $this->syncPermissionToRedis($args['pm_ids']);
        }
        if (array_key_exists('pqa_ids', $args)) {
            $this->projectUserRoleRepository->destroyWithRole($id, ERole::PQA);
            $membersRole = $roles->find(ERole::PQA);
            $members['user_ids'] = $args['pqa_ids'];
            $members['role_id'] = $membersRole->id;
            $this->projectUserRoleRepository->addMember($members);
        }
        if (array_key_exists('seller_ids', $args)) {
            $this->projectUserRoleRepository->destroyWithRole($id, ERole::SALES);
            $membersRole = $roles->find(ERole::SALES);
            $members['user_ids'] = $args['seller_ids'];
            $members['role_id'] = $membersRole->id;
            $this->projectUserRoleRepository->addMember($members);
        }
        if (array_key_exists('stakeholder_ids', $args)) {
            $this->projectStakeholderRepository->addStakeholder($id, $args['stakeholder_ids']);
        }

        return $project;
    }

    public function getGeneralInformation($id)
    {
        $project = $this->projectRepository->with('customers', 'pms', 'pqas', 'sellers')->find($id);
        if ($project) {
            $pivot = $this->projectUserRoleRepository->getPivotData($project->id, ['pagination' => false])->toArray();
            $project->user_role = $pivot;
            $stakeholderPivot = $this->projectStakeholderRepository->getIdsByProject([$project->id]);
            $stakeholderIds = $stakeholderPivot->pluck('stakeholder_id')->toArray();
            $project->pm_ids = $project->pms->pluck('user_id')->toArray();
            $project->pqa_ids = $project->pqas->pluck('user_id')->toArray();
            $project->seller_ids = $project->sellers->pluck('user_id')->toArray();
            $project->stakeholder_ids = $stakeholderIds;
        }

        return $project;
    }

    public function getRelationship($id, $relationship)
    {
        $project = $this->projectRepository->with($relationship)->find($id);

        return $project->{$relationship} ?? [];
    }

    public function getWeeks($id)
    {
        $project = $this->projectRepository->find($id);
        if (!$project) {
            return $project;
        }

        $weeks = $this->weeksBetweenTwoDates($project->start_date, now());

        return $weeks;
    }

    public function weeksBetweenTwoDates($startDate = null, $endDate = null)
    {
        $date1 = new DateTime($startDate);
        $date2 = new DateTime($endDate);

        $interval = $date1->diff($date2);
        $weeks = floor(($interval->days) / 7);
        if (($date1->format('N') > 1) && ($date1->format('D') != 'Sun')) {
            $different = '-' . ($date1->format('N') - 1) . ' Days';
            $date1 = $date1->modify($different);
        }
        $result = [];
        for ($i = 0; $i <= $weeks; $i++) {
            if ($i == 0) {
                $start_date = $date1->format('d/m');
                $date1->add(new DateInterval('P6D'));
            } else {
                $date1->add(new DateInterval('P6D'));
            }
            $result[] = 'Week ' . ($i + 1) . ' (' . $start_date . ' - ' . $date1->format('d/m') . ')';
            $date1->add(new DateInterval('P1D'));
            $start_date = $date1->format('d/m');
        }

        return $result;
    }

    public function getAllProject($args)
    {
        $fields = isset($args['fields']) ? $args['fields'] : '*';

        return $this->projectRepository->select($fields)->get();
    }

    public function getProjects($args)
    {
        $fields = $args['fields'] ?? '*';
        $relation = $args['relation'] ?? [];
        $keyWord = $args['key_word'] ?? '';
        $projectType = $args['project_type'] ?? [];
        $query = $this->projectRepository
            ->select($fields)
            ->with($relation)
            ->when($keyWord, fn ($query) => $query->where(
                fn ($q) => $q->orWhere('name', 'like', '%' . $keyWord . '%')
                    ->orWhere('code', 'like', '%' . $keyWord . '%')
            ))
            ->when(@$args['name'], fn ($query) => $query->orWhere('name', 'like', '%' . $keyWord . '%'))
            ->when(@$args['project_ids'], fn ($query) => $query->whereIn('id', (array)$args['project_ids']))
            ->when(@$args['division_ids'], fn ($query) => $query->whereIn('division_id', (array)$args['division_ids']))
            ->when(@$args['team_ids'], fn ($query) => $query->whereIn('team_id', (array)$args['team_ids']))
            ->when(@$args['statuses'], fn ($query) => $query->whereIn('status', (array)$args['statuses']))
            ->when(@$args['legal'], fn ($query) => $query->where('legal', $args['legal']))
            ->when(@$args['is_teams_webhook_url'], fn ($query) => $query->whereNotNull('teams_webhook_url'))
            ->when($projectType, fn ($query) => $query->whereIn('project_type', (array)$projectType));

        if (isset($args['with_trashed']) && intval($args['with_trashed']) === 1) {
            $query->withTrashed();
        }

        if (isset($args['disable_paginate'])) {
            return $query->get();
        }

        return $query->paginate($args['per_page'] ?? Config::get('define.projectPagination'));
    }

    public function checkUnclosedProjects($projectIds)
    {
        $args = [
            'project_ids' => $projectIds,
            'statuses' => array_diff(EProjectStatus::getAll(), (array)EProjectStatus::CLOSED),
            'disable_paginate' => true
        ];
        $projects = $this->getProjects($args);

        return $projects->count() == count($projectIds);
    }

    public function getProjectAllocations($args, $user_id)
    {
        $args['per_page'] = Enum::MAX_LIMIT;
        $query = $this->projectRepository->select(['projects.*', 'allocations.*', 'projects.id as project_id'])
            ->join('stages', 'stages.project_id', '=', 'projects.id')
            ->join('allocations', 'stages.id', '=', 'allocations.stage_id')
            ->where('allocations.user_id', $user_id)
            //            ->whereIn('stages.status', [EStageStatus::OPEN, EStageStatus::IN_PROGRESS])
            ->whereDate('allocations.start_date', '<=', now()->format('Y-m-d'))
            ->whereDate('allocations.end_date', '>=', now()->format('Y-m-d'))
            ->where('allocations.deleted_at', null);

        if (isset($args['disable_paginate'])) {
            return $query->get();
        }

        return $query->get()->toArray();
    }

    public function getProjectStats($args, $user_id)
    {
        $args['per_page'] = Enum::MAX_LIMIT;
        $query = $this->projectRepository->select('projects.*')
            ->join('project_role_user', 'project_role_user.project_id', '=', 'projects.id')
            ->where('project_role_user.user_id', $user_id);

        if (isset($args['disable_paginate'])) {
            return $query->get();
        }

        return $query->get()->toArray();
    }

    public function destroy($id)
    {
        $project = $this->projectRepository->find($id);
        if (!$project) {
            return [
                'message' => __('not found'),
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            ];
        }
        $projectType = $project->project_type;
        if ($projectType != EProjectType::OPPORTUNITY) {
            return [
                'message' => __('only_projects_of_the_opportunity_type_deleted'),
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            ];
        }

        return $this->projectRepository->destroy($id);
    }

    public function overview($id)
    {
        return $this->projectRepository->with(
            [
                'stages:id,project_id,start_date,replan_start_date',
                'stages.allocations',
                'allocations' => fn ($q) => $q->whereDate('allocations.start_date', '<', Carbon::now()),
                'approvedDailyReports:user_id,project_id,coefficient,work_date,actual_time,status',
                'stages'
            ],
        )->find($id);
    }

    public function expenseEE($id)
    {
        return $this->projectRepository->with('stages:id,project_id', 'stages.allocations', 'allocations')->find($id);
    }

    public function userBelongToProject($userIds)
    {
        return $this->projectUserRoleRepository->userBelongToProject($userIds);
    }

    public function updateProjectWF($args)
    {
        $projectId = $this->projectRepository->firstWhere([
            'code' => $args['code']
        ], ['id'])->id;

        $this->projectRepository
            ->where('id', $projectId)
            ->update([
                'project_type' => $args['project_type']
            ]);

        $this->projectUserRoleRepository->create([
            'user_id' => $args['pqa_id'],
            'project_id' => $projectId,
            'role_id' => ERole::PQA
        ]);
    }

    public function getProjectIdsByDivision($divisionIds)
    {
        $args['division_ids'] = $divisionIds;
        $args['fields'] = ['id'];

        return $this->projectRepository->getProjects($args)->pluck('id')->toArray();
    }

    public function getProjectsOfUser($userId, $args)
    {
        $user = $this->getAllUser(['user_ids' => [$userId]]);
        $division = $user->first()->division->name ?? null;
        $projectForAllUser = $this->projectRepository->select(['id', 'name'])
            ->whereIn('code', EProjectType::PROJECT_FOR_ALL_USER)
            ->when(@$division, fn ($query) => $query->orWhere('code', $division))
            ->get();
        $belongsToProject =  $this->projectUserRoleRepository->getProjectsOfUser($userId, $args);
        $projects = $projectForAllUser->merge($belongsToProject);
        return $projects;
    }

    public function getProjectMembers($projectIds)
    {
        return $this->projectUserRoleRepository->getUsersInProject($projectIds);
    }

    public function getProjectStatistics($args = [])
    {
        $firstOfMonth = Carbon::parse($args['month'])->firstOfMonth()->toDateString();
        $lastOfMonth = Carbon::parse($args['month'])->lastOfMonth()->toDateString();

        $projects = $this->projectRepository
            ->with('pms')
            ->when(
                in_array($args['position_id'] ?? null, EPosition::GROUP_DL),
                fn ($query) => $query->where('division_id', $args['division_id'])
            )
            ->orderBy('id', 'desc')
            ->paginate($args['limit'] ?? Config::get('define.per_page_default'))
            ->toArray();

        $projectTotalReportHour = $this->dailyReportRepository->getProjectTotalReportHour(['month' => $args['month']]);

        $projectTotalPendingReportHour = $projectTotalReportHour
            ->where('status', EDailyReport::PENDING_STATUS)
            ->pluck('hour', 'project_id')->toArray();

        $projectTotalApprovedReportHour = $projectTotalReportHour
            ->where('status', EDailyReport::SUCCESS_STATUS)
            ->pluck('hour', 'project_id')->toArray();

        // get total approved report hour of each project
        $projectReports = $this->dailyReportRepository->getList([
            'status' => EDailyReport::SUCCESS_STATUS,
            'from_date' => $firstOfMonth,
            'to_date' => $lastOfMonth,
        ])
            ->groupBy('project_id')
            ->toArray();

        // get total allocate hour of each project
        $projectAllocates = $this->allocationRepository->getProjectAllocates([
            'from' => $firstOfMonth,
            'to' => $lastOfMonth,
        ])
            ->groupBy('project_id')
            ->toArray();

        $projects['data'] = collect($projects['data'])
            ->map(function ($project) use (
                $projectTotalApprovedReportHour,
                $projectTotalPendingReportHour,
                $projectReports,
                $projectAllocates
            ) {
                return [
                    'id' => $project['id'],
                    'name' => $project['name'],
                    'division_id' => $project['division_id'],
                    'pm_ids' => array_column($project['pms'] ?? [], 'user_id'),
                    'approved_hour' => $projectTotalApprovedReportHour[$project['id']] ?? 0,
                    'pending_hour' => $projectTotalPendingReportHour[$project['id']] ?? 0,
                    'daily_reports' => $projectReports[$project['id']] ?? [],
                    'allocates' => $projectAllocates[$project['id']] ?? [],
                ];
            })
            ->toArray();

        return $projects;
    }

    public function checkDivisionProject($division_id, $project_id)
    {
        $res = $this->projectRepository->checkDivisionProject($division_id, $project_id);

        return (bool) $res;
    }

    public function getProjectsByKeyword($keyword)
    {
        return $this->projectRepository->getProjectsByKeyword($keyword);
    }

    public function getProjectsByCustomer($customerId)
    {
        return $this->projectRepository->getProjectsByCustomer($customerId);
    }

    public function getRawProjects($args)
    {
        return $this->projectRepository->getRawProjects($args);
    }

    public function syncTeamIdToProjects()
    {
        $members = $this->projectUserRoleRepository->getMembersByRole(ERole::PM);
        $userIds = $members->pluck('user_id')->unique()->toArray();
        $users = $this->getAllUser(['user_ids' => $userIds]);
        $projects = $this->projectRepository->select(['id', 'name', 'team_id'])->get();

        foreach ($projects as $project) {
            $pmIds = $project['pms']->pluck('user_id')->toArray();
            $pm = $users->whereIn('user_id', $pmIds)->first();

            if (empty($project['team_id'])) {
                $this->projectRepository->update(
                    $project['id'],
                    ['team_id' => $pm->team->id ?? null]
                );
            }
        }
    }

    public function getWorklogReport($args)
    {
        return $this->projectRepository->getWorklogReport($args);
    }

    public function getProjectMonthBudget($args)
    {
        $monthBudget = collect([]);
        foreach ($args['project_ids'] as $projectId) {
            $monthBudget[] = $this->projectMonthBudgetRepository->getByPeriod($projectId, $args['from'], $args['to'])->toArray();
        }
        $monthBudget = $monthBudget
            ->flatten(1)
            ->groupBy('month')
            ->map(function ($budget) {
                return $budget->sum('budget');
            })
            ->toArray();
        $months = $this->getMonthsBetweenFormatYm($args['from'], $args['to']);
        $budget = [];
        foreach ($months as $month) {
            $budget[] = [
                'month' => $month,
                'budget' => $monthBudget[$month] ?? 0,
            ];
        }
        return ['budget' => $budget];
    }

    public function getProjectByCode($args)
    {
        $project = $this->projectRepository->getProjectByCode($args);

        return ['project' => $project];
    }

    public function getPerformanceBySprint($projectId)
    {
        $sprints = Repository::getSprint()->getByProject($projectId);
        $defects = Repository::getDefect()->getByProject($projectId);
        $functions = Repository::getFunction()->getByProject($projectId);
        $qualityGates = Repository::getQualityGate()->getByProject([$projectId]);
        $dailyReports = $this->dailyReportRepository->getList([
            'project_id' => $projectId,
            'status' => EDailyReport::SUCCESS_STATUS,
        ]);
        $stages = Repository::getStage()->getAllocationByStageIds([$projectId]);
        $stageIds = $stages->pluck('id')->toArray();
        $allocations = $this->allocationRepository->getAllocationByStageIds($stageIds, []);

        $sprints->map(function ($sprint) use ($dailyReports, $defects, $qualityGates, $functions, $allocations) {
            $sprint->dailyReports = $dailyReports
                ->where('work_date', '>=', $sprint->start_date)
                ->where('work_date', '<=', $sprint->end_date)
                ->values();
            $sprint->allocations = $allocations
                ->where('start_date', '>=', $sprint->start_date)
                ->where('start_date', '<=', $sprint->end_date)
                ->where('end_date', '>=', $sprint->start_date)
                ->where('end_date', '<=', $sprint->end_date)
                ->values();
            $sprint->defects = $defects->where('sprint_id', $sprint->id)->values();
            $sprint->functions = $functions->where('sprint_id', $sprint->id)->values();
            $sprint->qualityGate = $qualityGates->where('sprint_id', $sprint->id)->first();
        });

        return $sprints;
    }

    /**
     * Insert performance data
     *
     * @param $projectId
     * @param $data
     * @return void
     */
    public function insertPerformanceData($projectId, $data)
    {
        $data = collect($data);
        $sprints = collect($data->get('sprints'));
        $functionCategories = collect($data->get('function_categories'));

        $functionCategoryMap = $this->prepareFunctionCategoryMap($functionCategories, $projectId);
        $sprintIdMap = $this->insertSprintsAndGetIdMap($projectId, $sprints);

        [$allFunctions, $allDefects, $allQualityGates] = $this->prepareAllData(
            $projectId, $sprints, $sprintIdMap, $functionCategoryMap
        );

        $this->bulkInsertData($allFunctions, $allDefects, $allQualityGates);
    }

    /**
     * Prepare function category map
     *
     * @param  Collection  $categoryNames
     * @param $projectId
     * @return array
     */
    private function prepareFunctionCategoryMap(Collection $categoryNames, $projectId): array
    {
        $existingFunctionCategories = Repository::getFunctionCategory()->list([
            'project_id' => $projectId,
            'name' => $categoryNames->toArray(),
        ]);

        $existingNames = $existingFunctionCategories->pluck('name')->all();
        $missingNames = $categoryNames->diff($existingNames)->values();

        if ($missingNames->isNotEmpty()) {
            $newRows = $missingNames->map(fn ($name) => [
                'name' => $name,
                'project_id' => $projectId,
            ]);

            Repository::getFunctionCategory()->insert($newRows->toArray());

            $existingFunctionCategories = Repository::getFunctionCategory()->list([
                'project_id' => $projectId,
                'name' => $categoryNames->toArray(),
            ]);
        }

        return $existingFunctionCategories->pluck('id', 'name')->toArray();
    }

    /**
     * Insert sprint data
     *
     * @param $projectId
     * @param  Collection  $sprints
     * @return array
     */
    private function insertSprintsAndGetIdMap($projectId, Collection $sprints): array
    {
        $sprintInsertData = $sprints->map(function ($sprint) {
            return collect($sprint)->except(['functions', 'defects', 'quality_gate'])->toArray();
        })->toArray();

        Repository::getSprint()->insert($sprintInsertData);

        // KeyBy by name or unique id
        return Repository::getSprint()->list([
            'project_id' => $projectId,
        ])
            ->keyBy('name')
            ->map(fn ($sprint) => $sprint->id)
            ->toArray();
    }

    /**
     * Prepare all data
     *
     * @param $projectId
     * @param  Collection  $sprints
     * @param  array  $sprintIdMap
     * @param  array  $functionCategoryMap
     * @return array
     */
    private function prepareAllData($projectId, Collection $sprints, array $sprintIdMap, array $functionCategoryMap): array
    {
        $allFunctions = collect();
        $allDefects = collect();
        $allQualityGates = collect();

        $sprints->each(function ($sprint) use (
            $projectId, $sprintIdMap, $functionCategoryMap, &$allFunctions, &$allDefects, &$allQualityGates
        ) {
            $sprintName = $sprint['name'];
            $sprintId = $sprintIdMap[$sprintName] ?? null;
            if (! $sprintId) return;

            $allFunctions->push(
                $this->mapFunctions($sprint['functions'], $projectId, $sprintId, $functionCategoryMap)
            );

            $allDefects->push(
                $this->mapDefects($sprint['defects'], $projectId, $sprintId)
            );

            $allQualityGates->push(
                $this->mapQualityGate($sprint['quality_gate'], $projectId, $sprintId)
            );
        });

        return [$allFunctions, $allDefects, $allQualityGates];
    }

    /**
     * Bulk insert data
     *
     * @param  Collection  $functions
     * @param  Collection  $defects
     * @param  Collection  $qualityGates
     * @return void
     */
    private function bulkInsertData(Collection $functions, Collection $defects, Collection $qualityGates): void
    {
        Repository::getFunction()->insert($functions->flatten(1)->toArray());
        Repository::getDefect()->insert($defects->flatten(1)->toArray());
        Repository::getQualityGate()->insert($qualityGates->toArray());
    }

    /**
     * Map quality gate
     *
     * @param  array  $functions
     * @param $projectId
     * @param $sprintId
     * @param  array  $categoryMap
     * @return Collection
     */
    private function mapFunctions(array $functions, $projectId, $sprintId, array $categoryMap): Collection
    {
        return collect($functions)->map(fn ($f) => collect($f)->merge([
            'project_id' => $projectId,
            'sprint_id' => $sprintId,
            'function_category_id' => $categoryMap[$f['function_category']] ?? null,
        ])->except('function_category'));
    }

    /**
     * Map defects
     *
     * @param  array  $defects
     * @param $projectId
     * @param $sprintId
     * @return Collection
     */
    private function mapDefects(array $defects, $projectId, $sprintId): Collection
    {
        return collect($defects)->map(fn ($d) => collect($d)->merge([
            'project_id' => $projectId,
            'sprint_id' => $sprintId,
        ]));
    }

    /**
     * Map quality gate
     *
     * @param  array  $qualityGate
     * @param $projectId
     * @param $sprintId
     * @return Collection
     */
    private function mapQualityGate(array $qualityGate, $projectId, $sprintId): Collection
    {
        return collect($qualityGate)->merge([
            'project_id' => $projectId,
            'sprint_id' => $sprintId,
        ]);
    }
}
