<?php

namespace App\Services\Api;

use App\Enums\ECommon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class ResponseFactory implements ResponseFactoryInterface
{
    /**
     * Make the success response.
     *
     * @param $message
     * @param $data
     * @param int $status
     * @return ResponseFactory|JsonResponse
     */
    public function success($message, $data = null, int $status = JsonResponse::HTTP_OK)
    {
        $response = [
            'code' => ECommon::RESPONSE_CODE_SUCCESS,
            'message' => $message,
        ];
        if (!is_null($data)) {
            $response['data'] = $data;
        }

        return $this->make($response, $status);
    }

    /**
     * Make the error response.
     *
     * @param $code
     * @param $message
     * @param int $status
     * @param array $errors
     * @return ResponseFactory|JsonResponse
     */
    public function error($code, $message, int $status, array $errors = [])
    {
        $response = [
            'code' => ECommon::RESPONSE_CODE_FAILURE,
            'message' => $message,
        ];
        if ($errors) {
            $response['errors'] = $errors;
        }

        return $this->make($response, $status);
    }

    /**
     * Make the response.
     *
     * @param mixed $data
     * @param int $status
     *
     * @return ResponseFactory|Response
     */
    private function make($data, int $status)
    {
        return response($data, $status);
    }
}
