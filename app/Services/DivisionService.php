<?php

namespace App\Services;

use App\Interfaces\DivisionRepositoryInterface;

class DivisionService
{
    private $divisionRepository;

    public function __construct(DivisionRepositoryInterface $divisionRepository)
    {
        $this->divisionRepository = $divisionRepository;
    }

    public function store($args)
    {
        return $this->divisionRepository->store($args);
    }

    public function getAll($args)
    {
        $division = $this->divisionRepository->getAll($args);

        return $division;
    }

    public function update($id, $args)
    {
        return $this->divisionRepository->update($id, $args);
    }

    public function getDetail($id)
    {
        return $this->divisionRepository->getDetail($id);
    }

    public function destroy($id)
    {
        $res = $this->divisionRepository->destroy($id);

        return (bool) $res;
    }
}
