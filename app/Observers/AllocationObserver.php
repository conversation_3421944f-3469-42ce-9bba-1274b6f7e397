<?php

namespace App\Observers;

use App\Enums\EActionLog;
use App\Models\Allocation;

class AllocationObserver
{
    private function createLog(Allocation $allocation, $event, $messageKey)
    {
        $allocation->load('stages');
        $user_id = request()->user_id;
    }

    /**
     * Handle the Allocation "created" event.
     *
     * @param  \App\Models\Allocation  $allocation
     * @return void
     */
    public function created(Allocation $allocation)
    {
        $this->createLog($allocation, EActionLog::ALLOCATION_ADDED, 'model_added');
    }

    /**
     * Handle the Allocation "updated" event.
     *
     * @param  \App\Models\Allocation  $allocation
     * @return void
     */
    public function updated(Allocation $allocation)
    {
        $this->createLog($allocation, EActionLog::ALLOCATION_UPDATED, 'model_updated');
    }

    /**
     * Handle the Allocation "deleted" event.
     *
     * @param  \App\Models\Allocation  $allocation
     * @return void
     */
    public function deleted(Allocation $allocation)
    {
        $this->createLog($allocation, EActionLog::ALLOCATION_DELETED, 'model_deleted');
    }

    /**
     * Handle the Allocation "restored" event.
     *
     * @param  \App\Models\Allocation  $allocation
     * @return void
     */
    public function restored(Allocation $allocation)
    {
        //
    }

    /**
     * Handle the Allocation "force deleted" event.
     *
     * @param  \App\Models\Allocation  $allocation
     * @return void
     */
    public function forceDeleted(Allocation $allocation)
    {
        //
    }
}
