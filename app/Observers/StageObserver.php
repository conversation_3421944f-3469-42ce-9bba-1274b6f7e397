<?php

namespace App\Observers;

use App\Enums\EActionLog;
use App\Jobs\ActivityLogChangeStage;
use App\Models\Stage;

class StageObserver
{
    private function createLog(Stage $stage, $event, $messageKey)
    {
        $stage->load('project');
        $user_id = request()->user_id;
        dispatch(new ActivityLogChangeStage($user_id, $stage, $messageKey, $event))
            ->delay(now()->addSecond())
            ->afterCommit();
    }

    /**
     * Handle the Stage "created" event.
     *
     * @param  \App\Models\Stage  $stage
     * @return void
     */
    public function created(Stage $stage)
    {
        $this->createLog($stage, EActionLog::STAGE_ADDED, 'stage_added');
    }

    /**
     * Handle the Stage "updated" event.
     *
     * @param  \App\Models\Stage  $stage
     * @return void
     */
    public function updated(Stage $stage)
    {
        // $this->createLog($stage, EActionLog::STAGE_UPDATED, 'stage_updated');
    }

    /**
     * Handle the Stage "deleted" event.
     *
     * @param  \App\Models\Stage  $stage
     * @return void
     */
    public function deleted(Stage $stage)
    {
        $stage->allocations()->delete();
        $stage->milestones()->delete();
        $stage->deliverables()->delete();
    }

    /**
     * Handle the Stage "restored" event.
     *
     * @param  \App\Models\Stage  $stage
     * @return void
     */
    public function restored(Stage $stage)
    {
        //
    }

    /**
     * Handle the Stage "force deleted" event.
     *
     * @param  \App\Models\Stage  $stage
     * @return void
     */
    public function forceDeleted(Stage $stage)
    {
        //
    }
}
