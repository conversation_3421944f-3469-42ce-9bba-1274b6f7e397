<?php

namespace App\Observers;

use App\Enums\EActionLog;
use App\Jobs\ActivityLogDeliverable;
use App\Models\Deliverable;

class DeliverableObserver
{
    private function createLog(Deliverable $deliverable, $messageKey, $event)
    {
        $deliverable->load('stages');
        $user_id = request()->user_id;
        dispatch(new ActivityLogDeliverable($user_id, $deliverable, $messageKey, $event))
            ->delay(now()->addSecond())
            ->afterCommit();
    }

    /**
     * Handle the Deliverable "created" event.
     *
     * @param  \App\Models\Deliverable  $deliverable
     * @return void
     */
    public function created(Deliverable $deliverable)
    {
        $this->createLog($deliverable, 'deliverable_added', EActionLog::DELIVERABLE_ADDED);
    }

    /**
     * Handle the Deliverable "updated" event.
     *
     * @param  \App\Models\Deliverable  $deliverable
     * @return void
     */
    public function updated(Deliverable $deliverable)
    {
        //$this->createLog($deliverable, 'deliverable_updated', EActionLog::DELIVERABLE_UPDATED);
    }

    /**
     * Handle the Deliverable "deleted" event.
     *
     * @param  \App\Models\Deliverable  $deliverable
     * @return void
     */
    public function deleted(Deliverable $deliverable)
    {
        //$this->createLog($deliverable, 'deliverable_deleted', EActionLog::DELIVERABLE_DELETED);
    }

    /**
     * Handle the Deliverable "restored" event.
     *
     * @param  \App\Models\Deliverable  $deliverable
     * @return void
     */
    public function restored(Deliverable $deliverable)
    {
    }

    /**
     * Handle the Deliverable "force deleted" event.
     *
     * @param  \App\Models\Deliverable  $deliverable
     * @return void
     */
    public function forceDeleted(Deliverable $deliverable)
    {
    }
}
