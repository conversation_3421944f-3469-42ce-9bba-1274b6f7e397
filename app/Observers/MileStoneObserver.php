<?php

namespace App\Observers;

use App\Enums\EActionLog;
use App\Jobs\ActivityLogMilestone;
use App\Models\Milestone;

class MileStoneObserver
{
    private function createLog(Milestone $milestone, $messageKey, $event)
    {
        $milestone->load('stages');
        $user_id = request()->user_id;
        dispatch(new ActivityLogMilestone($user_id, $milestone, $messageKey, $event))
            ->delay(now()->addSecond())
            ->afterCommit();
    }

    /**
     * Handle the MileStone "created" event.
     *
     * @param  \App\Models\MileStone  $mileStone
     * @return void
     */
    public function created(Milestone $milestone)
    {
        $this->createLog($milestone, 'milestone_added', EActionLog::MILESTONE_ADDED);
    }

    /**
     * Handle the MileStone "updated" event.
     *
     * @param  \App\Models\MileStone  $mileStone
     * @return void
     */
    public function updated(Milestone $mileStone)
    {
        //$this->createLog($mileStone, 'milestone_updated', EActionLog::MILESTONE_UPDATED);
    }

    /**
     * Handle the MileStone "deleted" event.
     *
     * @param  \App\Models\MileStone  $mileStone
     * @return void
     */
    public function deleted(Milestone $mileStone)
    {
        //$this->createLog($mileStone, 'milestone_deleted', EActionLog::MILESTONE_DELETED);
    }

    /**
     * Handle the MileStone "restored" event.
     *
     * @param  \App\Models\MileStone  $mileStone
     * @return void
     */
    public function restored(Milestone $mileStone)
    {
        //
    }

    /**
     * Handle the MileStone "force deleted" event.
     *
     * @param  \App\Models\MileStone  $mileStone
     * @return void
     */
    public function forceDeleted(Milestone $mileStone)
    {
        //
    }
}
