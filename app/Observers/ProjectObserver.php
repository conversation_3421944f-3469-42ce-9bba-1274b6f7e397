<?php

namespace App\Observers;

use App\Models\Project;

class ProjectObserver
{
    /**
     * Handle the Project "created" event.
     *
     * @param  \App\Models\Project  $project
     * @return void
     */
    public function created(Project $project)
    {
        //
    }

    /**
     * Handle the Project "updated" event.
     *
     * @param  \App\Models\Project  $project
     * @return void
     */
    public function updated(Project $project)
    {
        //
    }

    /**
     * Handle the Project "deleted" event.
     *
     * @param  \App\Models\Project  $project
     * @return void
     */
    public function deleted(Project $project)
    {
        $project->projectMonthBudgets()->delete();
        $project->dailyReports()->delete();
        $stages = $project->stages();

        $stages->each(function ($stage) {
            $stage->allocations()->delete();
            $stage->milestones()->delete();
            $stage->deliverables()->delete();
        });

        $stages->delete();

        $resourceRentalCosts = $project->resoureRentalCosts();

        $resourceRentalCosts->each(function ($resourceRentalCost) {
            $resourceRentalCost->monthCosts()->delete();
        });

        $resourceRentalCosts->delete();
    }

    /**
     * Handle the Project "restored" event.
     *
     * @param  \App\Models\Project  $project
     * @return void
     */
    public function restored(Project $project)
    {
        //
    }

    /**
     * Handle the Project "force deleted" event.
     *
     * @param  \App\Models\Project  $project
     * @return void
     */
    public function forceDeleted(Project $project)
    {
        $stages = $project->stages();
        $stages->each(function ($stage) {
            $stage->allocations()->delete();
            $stage->milestones()->delete();
            $stage->deliverables()->delete();
        });
        $stages->delete();
        // $project->members()->detach(['project_id' => $project->id]);
        $project->requirementChanges()->delete();
    }
}
