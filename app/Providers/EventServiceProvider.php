<?php

namespace App\Providers;

use App\Models\Allocation;
use App\Models\Deliverable;
use App\Models\Milestone;
use App\Models\Project;
use App\Models\Stage;
use App\Observers\AllocationObserver;
use App\Observers\DeliverableObserver;
use App\Observers\MileStoneObserver;
use App\Observers\ProjectObserver;
use App\Observers\StageObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
       Project::observe(ProjectObserver::class);
       Stage::observe(StageObserver::class);
       Deliverable::observe(DeliverableObserver::class);
       Milestone::observe(MileStoneObserver::class);
//        Allocation::observe(AllocationObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
