<?php

namespace App\Providers;

use App\Enums\ECommon;
use Carbon\Carbon;
use DateTime;
use Illuminate\Database\Events\TransactionBeginning;
use Illuminate\Database\Events\TransactionCommitted;
use Illuminate\Database\Events\TransactionRolledBack;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class DatabaseQueryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        if (!config('logging.enable_sql_log')) {
            return;
        }

        DB::listen(function ($query) {
            $sql = $query->sql;
            foreach ($query->bindings as $binding) {
                if (is_string($binding)) {
                    $binding = "'{$binding}'";
                } elseif ($binding === null) {
                    $binding = 'NULL';
                } elseif ($binding instanceof Carbon) {
                    $binding = "'{$binding->toDateTimeString()}'";
                } elseif ($binding instanceof DateTime) {
                    $binding = "'{$binding->format('Y-m-d H:i:s')}'";
                }

                $sql = preg_replace("/\?/", $binding, $sql, 1);
            }

            Log::channel(ECommon::LOG_CHANNEL_SQL)->debug('SQL', [
                'sql' => $sql,
                'time' => "$query->time ms"
            ]);
        });
        Event::listen(TransactionBeginning::class, function () {
            Log::channel(ECommon::LOG_CHANNEL_SQL)->debug('START TRANSACTION');
        });
        Event::listen(TransactionCommitted::class, function () {
            Log::channel(ECommon::LOG_CHANNEL_SQL)->debug('COMMIT');
        });
        Event::listen(TransactionRolledBack::class, function () {
            Log::channel(ECommon::LOG_CHANNEL_SQL)->debug('ROLLBACK');
        });
    }
}
