<?php

namespace App\Providers;

use App\Interfaces\ActivityLogRepositoryInterface;
use App\Interfaces\AllocationRepositoryInterface;
use App\Interfaces\CustomerRepositoryInterface;
use App\Interfaces\DefectRepositoryInterface;
use App\Interfaces\DeliverableRepositoryInterface;
use App\Interfaces\DivisionRepositoryInterface;
use App\Interfaces\FileEvidenceRepositoryInterface;
use App\Interfaces\FunctionCategoryRepositoryInterface;
use App\Interfaces\FunctionRepositoryInterface;
use App\Interfaces\LevelRepositoryInterface;
use App\Interfaces\MilestoneRepositoryInterface;
use App\Interfaces\PcvReportRepositoryInterface;
use App\Interfaces\PermissionRoleRepositoryInterface;
use App\Interfaces\PmReportRepositoryInterface;
use App\Interfaces\PositionRepositoryInterface;
use App\Interfaces\ProjectRepositoryInterface;
use App\Interfaces\ProjectSellerRepositoryInterface;
use App\Interfaces\ProjectStakeholderRepositoryInterface;
use App\Interfaces\ProjectUserRoleRepositoryInterface;
use App\Interfaces\QualityGateRepositoryInterface;
use App\Interfaces\RequirementChangeRepositoryInterface;
use App\Interfaces\RoleRepositoryInterface;
use App\Interfaces\SprintRepositoryInterface;
use App\Interfaces\StageRepositoryInterface;
use App\Interfaces\TechnologyRepositoryInterface;
use App\Interfaces\UserUnpaidLeaveRepositoryInterface;
use App\Interfaces\ProjectYearBudgetRepositoryInterface;
use App\Interfaces\ProjectMonthBudgetRepositoryInterface;
use App\Repositories\ActivityLogRepository;
use App\Repositories\AllocationRepository;
use App\Repositories\CustomerRepository;
use App\Repositories\DefectRepository;
use App\Repositories\DeliverableRepository;
use App\Repositories\DivisionRepository;
use App\Repositories\FileEvidenceRepository;
use App\Repositories\FunctionCategoryRepository;
use App\Repositories\FunctionRepository;
use App\Repositories\LevelRepository;
use App\Repositories\MilestoneRepository;
use App\Repositories\PcvReportRepository;
use App\Repositories\PermissionRoleRepository;
use App\Repositories\PmReportRepository;
use App\Repositories\PositionRepository;
use App\Repositories\ProjectRepository;
use App\Repositories\ProjectSellerRepository;
use App\Repositories\ProjectStakeholderRepository;
use App\Repositories\ProjectUserRoleRepository;
use App\Repositories\QualityGateRepository;
use App\Repositories\RequirementChangeRepository;
use App\Repositories\RoleRepository;
use App\Repositories\SprintRepository;
use App\Repositories\StageRepository;
use App\Repositories\TechnologyRepository;
use App\Repositories\UserUnpaidLeaveRepository;
use App\Repositories\ProjectYearBudgetRepository;
use App\Repositories\ProjectMonthBudgetRepository;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(ProjectRepositoryInterface::class, ProjectRepository::class);
        $this->app->singleton(ProjectUserRoleRepositoryInterface::class, ProjectUserRoleRepository::class);
        $this->app->singleton(CustomerRepositoryInterface::class, CustomerRepository::class);
        $this->app->singleton(ProjectSellerRepositoryInterface::class, ProjectSellerRepository::class);
        $this->app->singleton(TechnologyRepositoryInterface::class, TechnologyRepository::class);
        $this->app->singleton(PcvReportRepositoryInterface::class, PcvReportRepository::class);
        $this->app->singleton(AllocationRepositoryInterface::class, AllocationRepository::class);
        $this->app->singleton(StageRepositoryInterface::class, StageRepository::class);
        $this->app->singleton(MilestoneRepositoryInterface::class, MilestoneRepository::class);
        $this->app->singleton(DeliverableRepositoryInterface::class, DeliverableRepository::class);
        $this->app->singleton(RoleRepositoryInterface::class, RoleRepository::class);
        $this->app->singleton(RequirementChangeRepositoryInterface::class, RequirementChangeRepository::class);
        $this->app->singleton(ProjectStakeholderRepositoryInterface::class, ProjectStakeholderRepository::class);
        $this->app->singleton(FileEvidenceRepositoryInterface::class, FileEvidenceRepository::class);
        $this->app->singleton(PmReportRepositoryInterface::class, PmReportRepository::class);
        $this->app->singleton(ActivityLogRepositoryInterface::class, ActivityLogRepository::class);
        $this->app->singleton(PositionRepositoryInterface::class, PositionRepository::class);
        $this->app->singleton(DivisionRepositoryInterface::class, DivisionRepository::class);
        $this->app->singleton(LevelRepositoryInterface::class, LevelRepository::class);
        $this->app->singleton(PermissionRoleRepositoryInterface::class, PermissionRoleRepository::class);
        $this->app->singleton(UserUnpaidLeaveRepositoryInterface::class, UserUnpaidLeaveRepository::class);
        $this->app->singleton(ProjectYearBudgetRepositoryInterface::class, ProjectYearBudgetRepository::class);
        $this->app->singleton(ProjectMonthBudgetRepositoryInterface::class, ProjectMonthBudgetRepository::class);
        $this->app->singleton(SprintRepositoryInterface::class, SprintRepository::class);
        $this->app->singleton(FunctionCategoryRepositoryInterface::class, FunctionCategoryRepository::class);
        $this->app->singleton(FunctionRepositoryInterface::class, FunctionRepository::class);
        $this->app->singleton(QualityGateRepositoryInterface::class, QualityGateRepository::class);
        $this->app->singleton(DefectRepositoryInterface::class, DefectRepository::class);
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
