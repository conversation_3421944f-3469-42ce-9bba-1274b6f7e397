<?php

namespace App\Providers;

use App\Interfaces\QualityGateServiceInterface;
use App\Services\QualityGateService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Register Quality Gate Service
        $this->app->singleton(QualityGateServiceInterface::class, QualityGateService::class);

        if ($this->app->isLocal()) {
            //$this->app->register(TelescopeServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
