<?php

namespace App\Http\Controllers;

use App\Interfaces\QualityGateServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class ProjectQualityGateController extends Controller
{
    private QualityGateServiceInterface $qualityGateService;

    public function __construct(QualityGateServiceInterface $qualityGateService)
    {
        $this->qualityGateService = $qualityGateService;
    }

    /**
     * Store a project quality gate
     * This method demonstrates the usage of the storeProjectQualityGate function
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        DB::beginTransaction();
        
        try {
            $data = $request->only([
                'project_id',
                'quality_gate',
                'number_of_non_compliance',
                'number_of_process',
                'number_of_incident',
                'number_of_customer_complaint',
                'note'
            ]);

            // Validate required fields
            $request->validate([
                'project_id' => 'required|integer|exists:projects,id',
                'quality_gate' => 'nullable|integer|in:0,1,2',
                'number_of_non_compliance' => 'nullable|integer|min:0',
                'number_of_process' => 'nullable|integer|min:0',
                'number_of_incident' => 'nullable|integer|min:0',
                'number_of_customer_complaint' => 'nullable|integer|min:0',
                'note' => 'nullable|string|max:1000'
            ]);

            // Store the quality gate (does not return the created record)
            $this->qualityGateService->storeProjectQualityGate($data);

            DB::commit();

            return response()->json([
                'message' => 'Project quality gate stored successfully',
                'status' => Response::HTTP_CREATED
            ], Response::HTTP_CREATED);

        } catch (\InvalidArgumentException $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Validation error: ' . $e->getMessage(),
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY
            ], Response::HTTP_UNPROCESSABLE_ENTITY);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Project not found',
                'status' => Response::HTTP_NOT_FOUND
            ], Response::HTTP_NOT_FOUND);

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->getError($e);
        }
    }

    /**
     * Check if project has a quality gate
     *
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function hasQualityGate($projectId)
    {
        try {
            $hasQualityGate = $this->qualityGateService->hasProjectQualityGate($projectId);
            
            return response()->json([
                'project_id' => $projectId,
                'has_quality_gate' => $hasQualityGate
            ]);

        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    /**
     * Get project quality gate
     *
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($projectId)
    {
        try {
            $qualityGate = $this->qualityGateService->getProjectQualityGate($projectId);
            
            if (!$qualityGate) {
                return response()->json([
                    'message' => 'No quality gate found for this project',
                    'status' => Response::HTTP_NOT_FOUND
                ], Response::HTTP_NOT_FOUND);
            }

            return response()->json([
                'data' => $qualityGate
            ]);

        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }
}
