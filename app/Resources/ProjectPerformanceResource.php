<?php

namespace App\Resources;

use App\Enums\EDailyReport;
use function PHPUnit\Framework\isEmpty;

class ProjectPerformanceResource extends BaseJsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'project_id' => $this->project_id,
            'name' => $this->name,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'allocations' => $this->allocations,
            'defects' => $this->defects,
            'quality_gate' => $this->qualityGate,
            'functions' => $this->functions,
            'daily_reports' => $this->dailyReports,
        ];
    }
}
