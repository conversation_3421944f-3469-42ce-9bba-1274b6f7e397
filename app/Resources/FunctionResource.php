<?php

namespace App\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class FunctionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'project_id' => $this->project_id,
            'function_category_id' => $this->function_category_id,
            'sprint_id' => $this->sprint_id,
            'name' => $this->name,
            'story_point' => $this->story_point,
            'estimate' => $this->estimate,
            'work_completed' => $this->work_completed,
            'note' => $this->note,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'function_category' => $this->whenLoaded('functionCategory'),
            'sprint' => $this->whenLoaded('sprint'),
        ];
    }
}
