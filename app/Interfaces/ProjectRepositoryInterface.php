<?php

namespace App\Interfaces;

interface ProjectRepositoryInterface extends RepositoryInterface
{
    public function store();
    // public function update();
    // public function delete();
    // public function get();
    public function checkDivisionProject($division_id, $project_id);
    public function getProjectsByKeyword($keyword);
    public function getProjectsByCustomer($customerId);
}
