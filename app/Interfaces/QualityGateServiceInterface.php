<?php

namespace App\Interfaces;

interface QualityGateServiceInterface
{
    /**
     * Store project quality gate without returning the created record
     *
     * @param array $data
     * @return void
     */
    public function storeProjectQualityGate(array $data): void;

    /**
     * Check if project has a quality gate
     *
     * @param int $projectId
     * @return bool
     */
    public function hasProjectQualityGate(int $projectId): bool;

    /**
     * Get project quality gate
     *
     * @param int $projectId
     * @return mixed
     */
    public function getProjectQualityGate(int $projectId);
}
