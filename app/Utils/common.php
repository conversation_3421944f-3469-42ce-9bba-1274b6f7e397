<?php

if (! function_exists('setting')) {
    function setting($key = '', $default = null, $isCached = true)
    {
        $key = strtolower($key);

        return Setting::get($key, $default, $isCached);
    }
}

if (! function_exists('the_admin')) {
    function the_admin()
    {
        return setting('admin_email', config('app.admin_email'));
    }
}

if (! function_exists('name_to_desc')) {
    function name_to_desc($name)
    {
        $words = explode('.', $name);
        $words = array_map(function ($word) {
            if ($word == 'projects') {
                return 'Project';
            }

            switch ($word) {
                case 'index': return 'List'; break;
                case 'store': return 'Thêm'; break;
                case 'show': return 'Xem'; break;
                case 'update': return 'Sửa'; break;
                case 'destroy': return 'Xóa'; break;
            }

            $word = implode(' ', explode('-', $word));
            $word = ucwords($word);

            return $word;
        }, $words);

        $words = array_reverse($words);

        return implode(' ', $words);
    }
}

if (! function_exists('api_throw')) {
    function api_throw($code, $message)
    {
        throw new \Illuminate\Http\Exceptions\HttpResponseException(response()
            ->json([
                'code' => $code,
                'message' => $message,
            ], $code));
    }
}

if (! function_exists('the_name')) {
    function the_name()
    {
        return setting('name', config('app.name'));
    }
}

if (! function_exists('the_title')) {
    function the_title()
    {
        return setting('title', config('app.title'));
    }
}

if (! function_exists('the_desc')) {
    function the_desc()
    {
        return setting('description', config('app.title'));
    }
}

if (! function_exists('the_keywords')) {
    function the_keywords()
    {
        return setting('keywords', config('app.name'));
    }
}

if (! function_exists('the_copyright')) {
    function the_copyright()
    {
        return 'Copyright &copy; '.now()->year.' '.the_name().'.';
    }
}

if (! function_exists('the_facebook')) {
    function the_facebook()
    {
        return true;
    }
}

if (! function_exists('the_expert')) {
    function the_expert($title)
    {
        if (strlen($title) > \App\Constants\App::EXPERT_LIMIT) {
            return mb_substr($title, 0, \App\Constants\App::EXPERT_LIMIT).'...';
        }

        return $title;
    }
}

if (! function_exists('human_file_size')) {
    /**
     * Returns a human readable file size
     *
     * @param  int  $bytes
     * Bytes contains the size of the bytes to convert
     * @param  int  $decimals
     * Number of decimal places to be returned
     * @return string a string in human readable format
     *
     * */
    function human_file_size($bytes, $decimals = 2)
    {
        $sz = 'BKMGTPE';
        $factor = (int) floor((strlen($bytes) - 1) / 3);

        return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)).$sz[$factor];
    }
}

if (! function_exists('upload_max_size')) {
    function upload_max_size()
    {
        return \App\Helpers\FileHelper::convertToByte(ini_get('upload_max_filesize')) / 1024;
    }
}

if (! function_exists('array_implode')) {
    function array_implode($data, array $source = null)
    {
        if (! is_array($data)) {
            return $data;
        }

        if (empty($source)) {
            return ''.(string) (implode(',', $data));
        }

        $res = is_array($data) ? array_map(function ($id) use ($source) {
            return $source[$id] ?? null;
        }, $data) : $data ?? null;

        return ''.(string) (is_array($data) ? trim(implode(',', $res), ',') : $res);
    }
}

if (! function_exists('unique_id')) {
    function unique_id()
    {
        return \Illuminate\Support\Str::limit(\Illuminate\Support\Str::uuid()->getInteger()->toString(), 12, '');
    }
}

if (! function_exists('escape_like')) {
    function escape_like(string $value, string $char = '\\'): string
    {
        return str_replace(
            [$char, '%', '_'],
            [$char.$char, $char.'%', $char.'_'],
            $value
        );
    }
}
