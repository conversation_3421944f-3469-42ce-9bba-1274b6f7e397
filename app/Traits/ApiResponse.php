<?php

namespace App\Traits;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

trait ApiResponse
{
    /**
     * @param  int  $code
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function sendMessage($message = null, $code = null)
    {
        $payload = [
            'message' => $message ?? __('success'),
        ];
        if ($code) {
            $payload['code'] = $code;
        }

        return response($payload, Response::HTTP_CREATED);
    }

    /**
     * @param  array  $data
     * @param  int  $code
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function sendSuccess($data = [], $message = null)
    {
        $key = array_keys($data)[0];
        $data = $data[$key];

        $payload = [
            'message' => $message ?? __('success'),
            $key => $data,
        ];

        return response($payload, Response::HTTP_OK);
    }

    /**
     * @param  array  $data
     * @param  int  $code
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function sendCreated($data = [], $message = null)
    {
        $key = array_keys($data)[0];
        $data = $data[$key];

        $payload = [
            'message' => $message ?? __('create_succeeded'),
            $key => $data,
        ];

        return response($payload, Response::HTTP_CREATED);
    }

    /**
     * @param  array  $data
     * @param  int  $code
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function sendUpdated($data = [], $message = null)
    {
        $key = array_keys($data)[0];
        $data = $data[$key];

        $payload = [
            'message' => $message ?? __('update_succeeded'),
            $key => $data,
        ];

        return response($payload, Response::HTTP_ACCEPTED);
    }

    /**
     * @param $errors
     * @param  null  $key
     * @param  int  $code
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function sendValidate($message = null)
    {
        return response([
            'message' => $message ?? __('validate_failed'),
        ], Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * @param  null  $key
     * @param  int  $code
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function sendFailed($message = null, $code = null)
    {
        $payload = [
            'message' => $message ?? __('fail'),
        ];
        if ($code) {
            $payload['code'] = $code;
        }

        return response($payload, Response::HTTP_BAD_REQUEST);
    }

    /**
     * @param  string  $key
     * @param  \Exception  $exp
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function sendError($exp = null, $key = null)
    {
        $code = Response::HTTP_BAD_REQUEST;
        $debug = config('app.debug');

        $response = [
            'code' => $code,
            'message' => $key ? trans($key) : trans('general.failed'),
        ];

        if ($debug && $exp) {
            $response['message'] = $exp->getMessage();
            $response['debug'] = $exp->getTraceAsString();
        }

        return response($response, $code);
    }

    public function getError($e)
    {
        $error_code = str_replace('.', '-', microtime(true));
        $environment = env('APP_ENV') ?? '';
        $app_name = env('APP_NAME') ?? '';
        $err_message = $e->getMessage();
        $err_line = $e->getLine();
        $err_file = $e->getFile();
        $trace_content = $e->getTraceAsString();
        $pram_request = request() ? json_encode(request()->all()) : [];

        $teams_notice_content = [
            'text' => "<strong>[ $app_name - $environment]</strong>
                        <strong><b>Error code</b>: $error_code</strong>
                        <p><b>Param request</b>: $pram_request</p>
                        <p><b>Messages</b>: $err_message</p>
                        <p><b>File</b>: $err_file</p>
                        <p><b>Line</b>: $err_line</p>
                        <p><b>Trace content</b>: $trace_content</p>",
        ];

        $error = [
            'file' => $err_file,
            'line' => $err_line,
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ];

        Log::debug(json_encode($error));

        $this->sendTeamsNotice($teams_notice_content);

        return response()->json([
            'status' => 500,
            'messages' => __('Lỗi không biết') . ': ' . $error_code,
            'error' => $error,
        ], Response::HTTP_INTERNAL_SERVER_ERROR);
    }

    public function sendTeamsNotice($body)
    {
        if (env('TEAM_WEBHOOK_URL')) {
            $webhook = env('TEAM_WEBHOOK_URL');
            $client = new \GuzzleHttp\Client;
            $headers = ['Content-Type' => 'application/json'];
            $request = new \GuzzleHttp\Psr7\Request('POST', $webhook, $headers, json_encode($body));
            $client->send($request);
        }
    }

    public function sendNotFound($message = null)
    {
        return response([
            'code' => Response::HTTP_NOT_FOUND,
            'message' => $message ?? __('not_found'),
        ], Response::HTTP_NOT_FOUND);
    }

    public function sendNotOwner($message = null)
    {
        return response([
            'code' => Response::HTTP_FORBIDDEN,
            'message' => $message ?? __('not_owner'),
        ], Response::HTTP_FORBIDDEN);
    }

    public function sendDeleted($message = null)
    {
        $payload = [
            'message' => $message ?? __('delete_succeeded'),
        ];

        return response($payload, Response::HTTP_ACCEPTED);
    }
}
