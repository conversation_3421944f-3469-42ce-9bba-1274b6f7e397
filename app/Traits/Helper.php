<?php

namespace App\Traits;

use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

trait Helper
{
    public function decodeAccessToken($accessToken)
    {
        try {
            return JWT::decode($accessToken, new Key(env('JWT_SECRET'), env('JWT_ALGO')));
        } catch (SignatureInvalidException $exception) {
            return unauthorized(__('signature_verification_failed'), []);
        } catch (BeforeValidException $exception) {
            return unauthorized($exception->getMessage(), []);
        } catch (ExpiredException $exception) {
            return unauthorized(__('expired_token'), []);
        } catch (\Exception $exception) {
            return unauthorized(__('unauthorize'), []);
        }
    }

    public function getUserIdFromAccessToken($accessToken)
    {
        $decoded = $this->decodeAccessToken($accessToken);

        return $decoded->user_id;
    }

    public function numberOfWorkingDays($from, $to, $holiday = [])
    {
        $workingDays = [1, 2, 3, 4, 5]; # date format = N (1 = Monday, ...)
        $from = new DateTime($from);
        $to = new DateTime($to);
        $to->modify('+1 day');
        $interval = new DateInterval('P1D');
        $periods = new DatePeriod($from, $interval, $to);

        $days = 0;
        foreach ($periods as $period) {
            if (!in_array($period->format('N'), $workingDays)) continue;
            if (in_array($period->format('Y-m-d'), $holiday)) continue;
            // if (in_array($period->format('*-m-d'), $holidayDays)) continue;
            $days++;
        }

        return $days;
    }

    function getDatesInRange($startDate, $endDate, $ignoreWeekend = false)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        $dates = [];

        while ($start->lte($end)) {

            if (!$ignoreWeekend) {
                $dates[] = $start->toDateString();
            } else {
                if (!$start->isWeekend()) {
                    $dates[] = $start->toDateString();
                }
            }

            $start->addDay();
        }

        return $dates;
    }

    public function isSameArray($array1, $array2)
    {
        return count(array_diff($array1, $array2)) == 0 && count($array1) === count($array2);
    }

    public function getMonthsBetweenFormatYm($start_date, $end_date)
    {
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $start->modify('first day of this month');
        $end->modify('first day of next month');
        $interval = DateInterval::createFromDateString('1 month');
        $period = new DatePeriod($start, $interval, $end);
        $months = [];
        foreach ($period as $month) {
            $months[] = $month->format('Y-m');
        }

        return $months;
    }
}
