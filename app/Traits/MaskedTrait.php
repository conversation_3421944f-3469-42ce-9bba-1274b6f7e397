<?php

namespace App\Traits;

use App\Enums\ECommon;

trait MaskedTrait
{
    /**
     * Masked data with some key.
     *
     * @param $input
     * @return string
     */
    public function maskedData($input)
    {
        $data = $input['data'] ?? $input;
        foreach ($data as $key => &$value) {
            if (in_array($key, ECommon::MASKED_KEY)) {
                $value = '***';
            }
        }

        return json_encode($input);
    }
}
