<?php

namespace App\Traits;

use Illuminate\Support\Facades\Http;

trait UserData
{
    /**
     * @param $user_id
     * @return array
     */
    public function getUserData($user_id)
    {
        $user = Http::get(config('services.user') . 'sso/user/' . $user_id)->object()->data;
        $data = [
            'id' => $user_id,
            'name' => $user->name,
            'avatar' => $user->avatar,
            'position' => $user->position_name,
        ];

        return $data;
    }

    public function getAllUser($query = [])
    {
        if (!isset($query['fields'])) $query['fields'] = ['user_id', 'email', 'level', 'name', 'avatar', 'division', 'team', 'contract'];
        $users = Http::post(config('services.user') . 'users', $query)->object();

        return collect($users->data);
    }

    public function getHoliday()
    {
        $response = Http::get(config('services.hr') . 'holiday')->object();

        return collect($response->data);
    }
}
