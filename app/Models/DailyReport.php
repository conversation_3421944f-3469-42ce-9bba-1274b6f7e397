<?php

namespace App\Models;

use App\Enums\EDailyReport;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DailyReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'project_id',
        'tool_type',
        'issue_key',
        'title',
        'work_date',
        'process_type',
        'actual_time',
        'status',
        'link_backlog',
        'issue_properties',
        'description',
        'coefficient'
    ];

    protected $hidden = ['created_at', 'updated_at'];

    public function project()
    {
        return $this
            ->belongsTo(Project::class, 'project_id')
            ->select('id', 'name');
    }
}
