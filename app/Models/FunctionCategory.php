<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FunctionCategory extends Model
{
    use SoftDeletes;

    protected $table = 'function_categories';

    protected $fillable = [
        'id',
        'project_id',
        'name',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function projectFunctions()
    {
        return $this->hasMany(ProjectFunction::class);
    }
}
