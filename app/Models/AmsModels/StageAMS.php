<?php

namespace App\Models\AmsModels;

use Illuminate\Database\Eloquent\Model;

class StageAMS extends Model
{
    protected $connection = 'db_ams';

    protected $table = 'stages';

    public function deliverables()
    {
        return $this->hasMany(DeliverableAMS::class, 'stage_id', 'id');
    }

    public function milestones()
    {
        return $this->hasMany(MilestoneAMS::class, 'stage_id', 'id');
    }

    public function allocations()
    {
        return $this->hasMany(ResourceAMS::class, 'stage_id', 'id');
    }

    public function project()
    {
        return $this->belongsTo(ProjectAMS::class);
    }

    public function requirementChanges()
    {
        return $this->hasMany(RequirementChange::class);
    }

    public function replan()
    {
        return $this->hasMany(StageReplanAMS::class, 'stage_id', 'id');
    }
}
