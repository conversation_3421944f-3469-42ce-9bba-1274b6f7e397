<?php

namespace App\Models\AmsModels;

use Illuminate\Database\Eloquent\Model;

class RequirementChangeAMS extends Model
{
    protected $connection = 'db_ams';

    protected $table = 'project_change_request';

    public function requirementChangeData()
    {
        return $this->hasMany(RequirementChangeDataAMS::class, 'project_change_request_id', 'id');
    }

    public function requirementChangeDetail()
    {
        return $this->hasMany(RequirementChangeDetailAMS::class, 'project_change_request_id', 'id');
    }
}
