<?php

namespace App\Models\AmsModels;

use Illuminate\Database\Eloquent\Model;

class ProjectAMS extends Model
{
    protected $connection = 'db_ams';

    protected $table = 'projects';

    public function customers()
    {
        return $this->belongsTo(CustomerAMS::class, 'customer_id', 'id');
    }

    public function stages()
    {
        return $this->hasMany(StageAMS::class, 'project_id', 'id')->with('deliverables', 'milestones', 'allocations.user', 'replan');
    }

    public function requirementChanges()
    {
        return $this->hasMany(RequirementChangeAMS::class, 'project_id', 'id')->with('requirementChangeData', 'requirementChangeDetail');
    }

    public function members()
    {
        return $this->belongsToMany(UserAMS::class, 'members', 'project_id', 'user_id')->withPivot('role_acms');
    }

    public function pmReports()
    {
        return $this->hasMany(PmReportAMS::class, 'project_id', 'id')->with('user:id,id_acms');
    }
}
