<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectFunction extends Model
{
    use SoftDeletes;

    protected $table = 'functions';

    protected $fillable = [
        'id',
        'project_id',
        'function_category_id',
        'sprint_id',
        'name',
        'story_point',
        'estimate',
        'work_completed',
        'note',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function functionCategory()
    {
        return $this->belongsTo(FunctionCategory::class);
    }

    public function sprint()
    {
        return $this->belongsTo(Sprint::class);
    }
}
