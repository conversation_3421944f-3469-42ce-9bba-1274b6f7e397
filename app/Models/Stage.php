<?php

namespace App\Models;

use App\Enums\EStageType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Stage extends Model
{
    use SoftDeletes;

    protected $table = 'stages';

    protected $fillable = [
        'project_id', 'type', 'version', 'status', 'billable', 'budget', 'start_date', 'end_date',
        'replan_start_date', 'replan_end_date', 'replan_by', 'reason', 'description', 'created_at', 'updated_at',
    ];

    protected $appends = ['name'];

    public function deliverables()
    {
        return $this->hasMany(Deliverable::class, 'stage_id', 'id');
    }

    public function milestones()
    {
        return $this->hasMany(Milestone::class, 'stage_id', 'id');
    }

    public function allocations()
    {
        return $this->hasMany(Allocation::class, 'stage_id', 'id');
    }

    public function project()
    {
        return $this->belongsTo(Project::class)->withTrashed();
    }

    public function requirementChanges()
    {
        return $this->hasMany(RequirementChange::class);
    }

    public function getNameAttribute()
    {
        return EStageType::getActionString($this->type).' '.$this->version;
    }
}
