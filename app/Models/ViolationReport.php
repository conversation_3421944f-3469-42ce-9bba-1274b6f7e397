<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class ViolationReport extends Model
{
    use SoftDeletes;

    protected $table = 'violation_reports';

    protected $fillable = [
        'title', 'creator_id', 'handler_id',  'rule_id', 'violator_id', 'project_id', 'team_id', 'status',
        'violation_date', 'violation_content', 'processing_content', 'evidence_url', 'evidence_file_name',
        'result_url', 'result_file_name', 'result',
    ];

    protected $hidden = ['created_at', 'updated_at', 'deleted_at'];

    public function getEvidenceUrlAttribute($evidenceUrl)
    {
        if ($evidenceUrl) return Storage::disk('s3')->temporaryUrl($evidenceUrl, now()->addMinutes(30));

        return null;
    }

    public function getResultUrlAttribute($resultUrl)
    {
        if ($resultUrl) return Storage::disk('s3')->temporaryUrl($resultUrl, now()->addMinutes(30));

        return null;
    }

    public function project()
    {
        return $this->hasMany(Project::class, 'id', 'project_id');
    }

    public function rule()
    {
        return $this->hasMany(Rule::class, 'id', 'rule_id');
    }
}
