<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QualityGate extends Model
{
    use SoftDeletes;

    protected $table = 'quality_gates';

    protected $fillable = [
        'id',
        'project_id',
        'sprint_id',
        'quality_gate',
        'number_of_non_compliance',
        'number_of_process',
        'number_of_incident',
        'number_of_customer_complaint',
        'note',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function sprint()
    {
        return $this->belongsTo(Sprint::class);
    }
}
