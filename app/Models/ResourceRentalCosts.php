<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ResourceRentalCosts extends Model
{
    protected $fillable = [
        'project_id',
        'division_id',
        'year',
        'note'
    ];

    protected $hidden = ['created_at', 'updated_at'];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function monthCosts()
    {
        return $this->hasMany(ResourceRentalMonthCosts::class, 'resource_rental_cost_id');
    }
}
