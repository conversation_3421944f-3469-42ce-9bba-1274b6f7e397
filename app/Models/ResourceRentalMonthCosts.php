<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ResourceRentalMonthCosts extends Model
{
    protected $fillable = [
        'resource_rental_cost_id',
        'month',
        'cost',
    ];

    protected $hidden = ['created_at', 'updated_at'];

    public function resourceRentalCosts()
    {
        return $this->belongsTo(ResourceRentalCosts::class);
    }

    public function setMonth($month)
    {
        $this->month = $month;
    }
}
