<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectMonthBudget extends Model
{
    use SoftDeletes;

    protected $table = 'project_month_budget';

    protected $fillable = ['project_id', 'year', 'month', 'budget'];

    protected $hidden = ['created_at', 'updated_at'];

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }
}
