<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;

    const HUBSPOT_ID = 24417680;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'email',
        'phone',
        'address',
        'note',
        'city',
        'domain',
        'industry',
        'state',
        'hubspot_id',
        'original_name',
        'supporter',
    ];

    protected $hidden = ['created_at', 'updated_at', 'pivot'];

    protected $appends = ['url'];

    public function projects()
    {
        return $this->belongsToMany(Project::class);
    }

    public function getUrlAttribute()
    {
        $appId = self::HUBSPOT_ID;
        $url = (isset($this->hubspot_id)) ? "https://app.hubspot.com/contacts/{$appId}/company/{$this->hubspot_id}" : '';
        return $url;
    }

    public function unitPrices()
    {
        return $this->hasMany(UnitPrice::class);
    }
}
