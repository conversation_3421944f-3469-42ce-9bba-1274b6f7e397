<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class FileEvidence extends Model
{
    use HasFactory;

    protected $fillable = ['requirement_change_id', 'url', 'file_type',  'file_name'];

    // protected $guarded = ['id'];

    protected $casts = [
        'created_at' => 'date:Y-m-d',
        'updated_at' => 'date:Y-m-d',
    ];

    public function getUrlAttribute($url)
    {
        if ($url) return env('LINK_S3') . $url;

        return null;
    }
}
