<?php

namespace App\Models;

use App\Enums\EPosition;
use Illuminate\Database\Eloquent\Model;

class Project<PERSON>oleUser extends Model
{
    protected $table = 'project_role_user';

    protected $fillable = ['user_id', 'project_id', 'role_id', 'position_in_project'];

    public $timestamps = false;

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function getPositionInProjectAttribute($value)
    {
        return EPosition::getActionString($value);
    }
}
