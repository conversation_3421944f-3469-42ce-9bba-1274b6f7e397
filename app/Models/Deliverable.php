<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Deliverable extends Model
{
    use SoftDeletes;

    protected $table = 'deliverables';

    protected $fillable = ['stage_id', 'name', 'expected_date', 'status', 'replan_to_date', 'replan_by', 'reason', 'created_at', 'updated_at', 'actual_date'];

    public function stages()
    {
        return $this->belongsTo(Stage::class, 'stage_id');
    }
}
