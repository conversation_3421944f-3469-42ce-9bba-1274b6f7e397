<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RequirementChange extends Model
{
    protected $table = 'requirement_change';

    protected $timestamp = true;

    protected $fillable = [
        'project_id', 'stage_id', 'code', 'type', 'status', 'priority', 'request_by', 'request_date', 'category', 'title',
        'content', 'detail_content', 'impact', 'impacted_roles', 'release_date', 'cost', 'note',
    ];

    public function projects()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function stages()
    {
        return $this->belongsTo(Stage::class, 'stage_id');
    }

    public function file_evidences()
    {
        return $this->hasMany(FileEvidence::class);
    }
}
