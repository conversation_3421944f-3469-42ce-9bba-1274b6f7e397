<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WeeklyReport extends Model
{
    use SoftDeletes;

    protected $table = 'weekly_reports';

    protected $guarded = ['pm_id', 'dm_id', 'pqa_id', 'pic_id'];

    protected $hidden = ['created_at', 'updated_at', 'deleted_at'];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($weeklyReport) {
            $userId = request()->input('user_id');
            $arrays = [
                'pm'  => ['id' => 'pm_id', 'status' => 'pm_status', 'note' => 'pm_note'],
                'dm'  => ['id' => 'dm_id', 'status' => 'dm_status', 'note' => 'dm_note'],
                'pqa' => ['id' => 'pqa_id', 'status' => 'pqa_status', 'note' => 'pqa_note'],
                'pic' => ['id' => 'pic_id', 'status' => 'pic_status', 'note' => 'pic_note'],
            ];
            foreach ($arrays as $value) {
                $status = $value['status'];
                $note = $value['note'];
                $id = $value['id'];
                if ($weeklyReport->exists) {
                    // case update
                    $originalStatus = $weeklyReport->getOriginal($status);
                    $originalNote = $weeklyReport->getOriginal($note);
                    if ($weeklyReport->$status != $originalStatus || $weeklyReport->$note != $originalNote) {
                        $weeklyReport->$id = $userId;
                    }
                } else {
                    // case create
                    if (@$weeklyReport->$status || @$weeklyReport->$note) {
                        $weeklyReport->$id = $userId;
                    }
                }
            }
        });
    }
}
