<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PmReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'project_id', 'date', 'week', 'template_id', 'cost_status', 'cost_comment', 'quality_status',
        'quality_comment', 'timeliness_status', 'timeliness_comment', 'process_status', 'process_comment',
        'customer_feedback_status', 'customer_feedback_comment', 'note',
    ];

    protected $hidden = ['created_at', 'pivot'];

    public function projects()
    {
        return $this->belongsTo(Project::class, 'project_id', 'id')->select('id', 'name', 'code', 'division_id');
    }

    public function pcv()
    {
        return $this->hasMany(PcvReport::class, 'date', 'date');
    }
}
