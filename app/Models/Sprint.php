<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Sprint extends Model
{
    use SoftDeletes;

    protected $table = 'sprints';

    protected $fillable = [
        'id',
        'project_id',
        'name',
        'start_date',
        'end_date',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function functions()
    {
        return $this->hasMany(ProjectFunction::class);
    }

    public function qualityGates()
    {
        return $this->hasMany(QualityGate::class);
    }

    public function defects()
    {
        return $this->hasMany(Defect::class);
    }
}
