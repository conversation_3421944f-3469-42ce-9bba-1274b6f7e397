<?php

namespace App\Console\Commands;

use App\Enums\EProjectStatus;
use App\Enums\EReplanBy;
use App\Enums\ERequirementChangeImpactedRole;
use App\Enums\ERequirementChangePriority;
use App\Enums\ERequirementChangeRequestBy;
use App\Enums\ERequirementChangeStatus;
use App\Enums\ERequirementChangeType;
use App\Enums\ERole;
use App\Models\Allocation;
use App\Models\AmsModels\ProjectAMS;
use App\Models\AmsModels\UserAMS;
use App\Models\Deliverable;
use App\Models\Milestone;
use App\Models\PmReport;
use App\Models\Project;
use App\Models\ProjectRoleUser;
use App\Models\RequirementChange;
use App\Models\Stage;
use App\Traits\UserData;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MigrateAmsDb extends Command
{
    use UserData;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ams:migrate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate database AMS';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $thorDivisionId = 15;
        $usersACMS = $this->getAllUser();
        $usersAMS = UserAMS::get();
        $usersAMS->each(function ($userAMS) use ($usersACMS) {
            $userACMS = $usersACMS->where('email', $userAMS->email)->first();
            if ($userACMS) {
                $userAMS->id_acms = $userACMS->user_id;
                $userAMS->save();
            } else {
                Log::channel('daily')->info($userAMS->email);
            }
        });

        $oldProjects = ProjectAMS::with([
            'customers', 'stages', 'members:id,email,id_acms', 'pmReports',
            'requirementChanges' => function ($q) {
                $q->where('status', 1);
            },
        ])->where('department_id', '<>', $thorDivisionId)->get();

        $oldProjects->each(function ($project) {
            $projectData = json_decode($project->data);
            $dataInsert = $project->toArray();
            $dataInsert['name'] = $project->title;
            $dataInsert['division_id'] = $project->department_id;
            $dataInsert['status'] = $this->reMapProjectStatus($project->status);
            $dataInsert['contract_type'] = $this->reMapContractType($project->contract_type);
            $dataInsert['project_type'] = 1;
            $dataInsert['rank'] = $this->reMapRank($project->rank);
            $dataInsert['scope'] = $projectData->scope ?? null;
            $dataInsert['communication'] = $projectData->communication ?? null;
            $dataInsert['description'] = $projectData->general ?? null;
            $dataInsert['contract_information'] = $projectData->contract ?? null;
            $dataInsert['critical'] = $projectData->critical ?? null;
            $dataInsert['note'] = '';
            $newProject = new Project;
            $newProject->fill($dataInsert)->save();
            $newProjectId = $newProject->id;

            $deliFillable = new Deliverable;
            $deliFillable = $deliFillable->getFillable();
            $mileStoneFillable = new Milestone;
            $mileStoneFillable = $mileStoneFillable->getFillable();
            $allocationFillable = new Allocation;
            $allocationFillable = $allocationFillable->getFillable();
            $pmReportFillable = new PmReport;
            $pmReportFillable = $pmReportFillable->getFillable();
            $requirementChangeFillable = new RequirementChange;
            $requirementChangeFillable = $requirementChangeFillable->getFillable();
            $project->requirementChanges->each(function ($requirementChange) use ($newProjectId, $requirementChangeFillable) {
                $dataInsert = $requirementChange;
                $dataInsert->project_id = $newProjectId;
                if (isset($requirementChange->requirementChangeDetail)) {
                    $requirementChangeDetail = $requirementChange->requirementChangeDetail->sortBy('created_at', SORT_NATURAL)->last();
                    $dataInsert->code = $requirementChangeDetail->change_request_code ?? '';
                    $dataInsert->type = $requirementChangeDetail->type ?? ERequirementChangeType::FEE;
                    $dataInsert->status = $requirementChangeDetail->status ?? ERequirementChangeStatus::DONE;
                    $dataInsert->priority = $requirementChangeDetail->priority ?? ERequirementChangePriority::LOW;
                    $dataInsert->category = $requirementChangeDetail->category ?? '';
                    $dataInsert->request_by = ERequirementChangeRequestBy::CUSTOMER;
                    $dataInsert->request_date = $requirementChange->month;
                    $dataInsert->title = $requirementChangeDetail->title ?? '';
                    $dataInsert->content = $requirementChangeDetail->content ?? '';
                    $dataInsert->detail_content = $requirementChangeDetail->detailed_content ?? '';
                    $dataInsert->impact = $requirementChangeDetail->impact ?? '';
                    $dataInsert->impacted_roles = ERequirementChangeImpactedRole::ALL;
                    $dataInsert->release_date = $requirementChangeDetail->deadline ?? '2000-01-01';
                    $dataInsert->cost = $requirementChangeDetail->cost ?? '0.000';
                    $dataInsert->note = $requirementChangeDetail->note ?? '';
                    $dataInsert = $requirementChange->only($requirementChangeFillable);
                    $newRequirementChange = RequirementChange::create($dataInsert);
                    $newRequirementChangeId = $newRequirementChange->id;
                }
                if (isset($requirementChange->requirementChangeData)) {
                    $requirementChangeData = $requirementChange->requirementChangeData;
                    $fileEvidence = $requirementChangeData->map(function ($evidence) use ($newRequirementChangeId) {
                        $fileEvidence['url'] = $evidence->file_url;
                        $fileEvidence['file_type'] = 1;
                        $fileEvidence['file_name'] = $evidence->file_name;
                        $fileEvidence['requirement_change_id'] = $newRequirementChangeId;

                        return $fileEvidence;
                    });
                    $newRequirementChange->file_evidences()->insert($fileEvidence->toArray());
                }
            });
            $members = [];
            $version = [];
            collect($project->stages)->each(function ($stage) use ($deliFillable, $mileStoneFillable, $allocationFillable, $newProjectId, &$members, $version) {
                $replan = $stage->replan->last();
                $newStage = new Stage;
                $stage->start_date = $stage->from_at;
                $stage->end_date = $stage->to_at;
                $stage->type = $this->reMapStageType($stage->title ?? 'Sprint');
                $version[$stage->type][] = $stage->type;
                $stage->version = count($version[$stage->type]);
                $stage->project_id = $newProjectId;
                $stage->description = $stage->note;
                if (isset($replan)) {
                    $stage->reason = $replan->replan_reason;
                    $stage->replan_by = $replan->replan_type;
                    $stage->replan_start_date = $stage->start_date;
                    $stage->replan_end_date = $replan->replan_date;
                }
                $newStage->fill($stage->toArray())->save();
                $newStageId = $newStage->id;

                $deliverables = $stage->deliverables->map(function ($delivery) use ($deliFillable, $newStageId) {
                    $delivery->name = $delivery->title;
                    $delivery->expected_date = $delivery->plan_at ?? null;
                    $delivery->replan_to_date = $delivery->replan_at ?? null;
                    $delivery->stage_id = $newStageId;
                    $delivery->actual_date = $delivery->actual_at;
                    if ($delivery->replan_to_date != null) $delivery->replan_by = EReplanBy::BY_CUSTOMER;
                    $delivery = $delivery->only($deliFillable);
                    return $delivery;
                })->toArray();
                $newStage->deliverables()->insert($deliverables);

                $milestones = $stage->milestones->map(function ($milestone) use ($mileStoneFillable) {
                    $milestone->name = $milestone->title;
                    $milestone->expected_date = $milestone->milestone_date ?? null;
                    $milestone->type = $milestone->category ?? 1;
                    $milestone = $milestone->only($mileStoneFillable);

                    return $milestone;
                })->toArray();
                $newStage->milestones()->insert($milestones);

                $allocations = $stage->allocations->map(function ($allocation) use ($allocationFillable, $newStageId, &$members, $newProjectId) {
                    if ($allocation->user->id_acms) {
                        $allocation->stage_id = $newStageId;
                        $allocation->start_date = $allocation->from_at;
                        $allocation->end_date = $allocation->to_at;
                        $allocation->user_id = $allocation->user->id_acms ?? null;
                        $allocation->coefficient = 1;
                        $allocation->role_id = $this->reMapAllocationRole($allocation->role ?? ERole::DEVELOPER);
                        $allocation = $allocation->only($allocationFillable);

                        $members[] = [
                            'project_id' => $newProjectId,
                            'user_id' => $allocation['user_id'],
                            'role_id' => $allocation['role_id'] ?? ERole::DEVELOPER,
                        ];

                        return $allocation;
                    }
                })->filter()->toArray();
                $newStage->allocations()->insert($allocations);
            });
            $members = array_map('unserialize', array_unique(array_map('serialize', $members)));
            ProjectRoleUser::insert($members);
            collect($project->members)->each(function ($member) use ($newProjectId) {
                if ($member->id_acms) {
                    ProjectRoleUser::create([
                        'project_id' => $newProjectId,
                        'user_id' => $member->id_acms,
                        'role_id' => $member->pivot->role_acms ?? ERole::DEVELOPER,
                    ]);
                }
            });
            $pmReports = collect($project->pmReports)->map(function ($pmReport) use ($newProjectId, $pmReportFillable) {
                $pmReport->project_id = $newProjectId;
                $pmReport->user_id = $pmReport->user->id_acms ?? 0;
                $date = Carbon::parse($pmReport->created_at);
                $pmReport->week = $date->weekOfYear;
                $pmReport->date = $date->format('Y-m-d');
                $pmReport->cost_status = $this->reMapEvaluateStatus($pmReport->cost);
                $pmReport->quality_status = $this->reMapEvaluateStatus($pmReport->quality);
                $pmReport->process_status = $this->reMapEvaluateStatus($pmReport->process);
                $pmReport->timeliness_status = $this->reMapEvaluateStatus($pmReport->timeliness);
                $pmReport->customer_feedback_status = $this->reMapEvaluateStatus($pmReport->customer_feedback_status);
                $pmReport = $pmReport->only($pmReportFillable);

                return $pmReport;
            })->toArray();
            PmReport::insert($pmReports);
        });
        echo 'Success !';
    }

    public function reMapProjectStatus($status)
    {
        switch ($status) {
            case 1:
                return EProjectStatus::OPEN;
                break;
            case 2:
                return EProjectStatus::CLOSED;
                break;
            case 3:
                return EProjectStatus::IN_PROGRESS;
                break;
            case 4:
                return EProjectStatus::CANCELED;
                break;
            case 8:
                return EProjectStatus::PENDING;
                break;
            case 9:
                return EProjectStatus::GUARANTEE;
                break;

            default:
                return EProjectStatus::OPEN;
                break;
        }
    }

    public function reMapContractType($type)
    {
        switch ($type) {
            case 1:
                return 2;
                break;

            default:
                return 1;
                break;
        }
    }

    public function reMapRank($value)
    {
        switch ($value) {
            case 1:
                return 'A';
                break;

            case 2:
                return 'B';
                break;

            case 3:
                return 'C';
                break;

            default:
                return 'D';
                break;
        }
    }

    public function reMapEvaluateStatus($value)
    {
        switch ($value) {
            case 1:
                return 'good';
                break;

            case 2:
                return 'warning';
                break;

            case 3:
                return 'serious';
                break;

            default:
                return 'na';
                break;
        }
    }

    public function reMapAllocationRole($value)
    {
        switch ($value) {
            case 1:
                return ERole::PM;
                break;
            case 2:
                return ERole::DEVELOPER;
                break;
            case 3:
                return ERole::TESTER;
                break;
            case 4:
                return ERole::BA;
                break;
            case 5:
                return ERole::TECH_LEAD;
                break;
            case 6:
                return ERole::TEST_LEAD;
                break;
            case 7:
                return ERole::COMTOR;
                break;
            case 8:
                return ERole::BRSE;
                break;
            case 9:
                return ERole::PQA;
                break;
            case 10:
                return ERole::DESIGNER;
                break;
            default:
                return ERole::DEVELOPER;
                break;
        }
    }

    public function reMapStageType($value)
    {
        if (
            $value == 'Transition & Termination'
            || $value == 'Transition and Termination'
            || $value == 'Transition'
            || $value == 'Termination'
        ) {
            return 5;
        } elseif ($value == 'Solution') {
            return 3;
        } elseif ($value == 'Construction') {
            return 4;
        } elseif ($value == 'Guarantee') {
            return 6;
        } elseif ($value == 'Labo') {
            return 2;
        }

        return 1;
    }
}
