<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Project;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $thorDivisionId = 15;
            $projects = Project::where('division_id', '<>', $thorDivisionId)->get();
            $projects->each(function ($project) {
                $project->deliverables()->delete();
                $project->milestones()->delete();
                $project->allocations()->delete();
                $project->stages()->delete();
                $project->memberPivot()->delete();
                $requirementChanges = $project->requirementChanges();
                $requirementChanges->each(function ($cr) {
                    $cr->file_evidences()->delete();
                    $cr->delete();
                });
                $project->requirementChanges()->delete();
                $project->pmReports()->delete();
                // $project->activityLogs()->delete();
                $project->delete();
            });

            DB::commit();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
            var_dump($e->getTraceAsString());
        }
    }
}
