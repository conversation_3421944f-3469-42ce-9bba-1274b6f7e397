<?php

namespace App\Console\Commands;

use App\Models\AmsModels\StageAMS;
use App\Models\Stage;
use Illuminate\Console\Command;

class MigrateStageReplan extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ams:migrate-stage-replan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $stagesAMS = StageAMS::with('replan')->get();
        $stagesAMS->each(function ($stageAMS) {
            $replan = $stageAMS->replan->last();
            $stage = Stage::where([
                ['project_id','=', $stageAMS->project_id],
                ['start_date','=', $stageAMS->from_at],
                ['end_date','=', $stageAMS->to_at],
                ['budget','=', $stageAMS->budget],
                ['billable','=', $stageAMS->billable],
            ])->first();
            if($stage && $replan){
                $stage->replan_start_date = $stage->start_date;
                $stage->replan_end_date = $replan->replan_date;
                $stage->reason = $replan->replan_reason;
                $stage->replan_by = $replan->replan_type;
                $stage->description = $stageAMS->note;
                $stage->save();
            }
        });
    }
}
