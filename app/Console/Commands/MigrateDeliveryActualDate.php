<?php

namespace App\Console\Commands;

use App\Enums\EReplanBy;
use App\Models\AmsModels\StageAMS;
use App\Models\Stage;
use Illuminate\Console\Command;

class MigrateDeliveryActualDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ams:migrate-delivery-actual-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $stagesAMS = StageAMS::with('deliverables')->find(2018);
        $stagesAMS->each(function ($stageAMS) {

            $stage = Stage::with('deliverables')->where([
                ['project_id', '=', $stageAMS->project_id],
                ['start_date', '=', $stageAMS->from_at],
                ['end_date', '=', $stageAMS->to_at],
                ['budget', '=', $stageAMS->budget],
                ['billable', '=', $stageAMS->billable],
            ])->first();

            $deliveryAMS = $stageAMS->deliverables;
            if (isset($stage->deliverables)) {
                $stage->deliverables->each(function ($delivery) use ($deliveryAMS) {
                    $deliveryAMS = $deliveryAMS
                        ->where('title', $delivery->name)
                        ->where('plan_at', $delivery->expected_date)
                        ->where('replan_at', $delivery->replan_to_date)
                        ->where('status', $delivery->status);
                    if ($deliveryAMS->isNotEmpty()) {
                        $deliveryAMS = $deliveryAMS->first();
                        $delivery->actual_date = $deliveryAMS->actual_at;
                        if ($delivery->replan_to_date != null) $delivery->replan_by = EReplanBy::BY_CUSTOMER;
                        $delivery->save();
                    }
                });
            }
        });
    }
}
