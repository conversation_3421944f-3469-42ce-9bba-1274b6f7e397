<?php

namespace App\Console\Commands;

use App\Services\ProjectService;
use App\Traits\UserData;
use Illuminate\Console\Command;

class SyncTeamToProject extends Command
{
    use UserData;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:team-id-to-project';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync team id to project';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(ProjectService $projectService)
    {
        $projectService->syncTeamIdToProjects();

        $this->info('Sync team id to project successfully');

        return 0;
    }
}
