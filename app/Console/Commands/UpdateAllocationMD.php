<?php

namespace App\Console\Commands;

use App\Services\AllocationService;
use App\Traits\Helper;
use App\Traits\UserData;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Console\Command;

class UpdateAllocationMD extends Command
{
    use UserData, Helper;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:allocation-md';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(AllocationService $allocationService)
    {
        $holiday = $this->getHoliday();
        $holiday = collect($holiday)->map(function ($holiday) {
            $startDate = Carbon::createFromFormat('d/m/Y', $holiday->start_date)->format('Y-m-d');
            $endDate = Carbon::createFromFormat('d/m/Y', $holiday->end_date)->format('Y-m-d');
            $dateRange = CarbonPeriod::create($startDate, $endDate);
            return array_map(fn ($date) => $date->format('Y-m-d'), iterator_to_array($dateRange));
        });
        $holiday = collect($holiday)->flatten()->toArray();
        $allocations = $allocationService->getAll();
        collect($allocations)->each(function($allocate) use($holiday){
            $allocate->man_day = $this->numberOfWorkingDays($allocate->start_date, $allocate->end_date, $holiday);
            $allocate->save();
        });
    }
}
