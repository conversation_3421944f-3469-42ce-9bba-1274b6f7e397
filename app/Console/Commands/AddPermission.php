<?php

namespace App\Console\Commands;

use App\Models\Permission;
use Illuminate\Console\Command;

/**
 * Class AddPermission
 * @package App\Console\Commands
 */
class AddPermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:add {route} {type} {group}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add permission';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $route = $this->argument('route');
        $type = $this->argument('type');
        $group = $this->argument('group');

        Permission::create([
            'name' => $route,
            'type' => $type,
            'group' => $group,
        ]);
        $this->info($route);
        $this->info('Done');
    }
}
