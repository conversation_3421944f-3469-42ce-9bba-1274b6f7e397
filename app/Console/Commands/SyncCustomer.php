<?php

namespace App\Console\Commands;

use App\Models\Customer;
use HubSpot\Client\Crm\Contacts\Model\SimplePublicObjectWithAssociations;
use Illuminate\Console\Command;
use App\Http\Integrations\Hubspot\Requests\GetCustomerRequest;
use Illuminate\Support\Facades\DB;

/**
 * Class SyncCustomer
 * @package App\Console\Commands
 */
class SyncCustomer extends Command
{
    const EMAIL = '<EMAIL>';
    const PREFIX = 'KH';
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customers:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync customer from Hubspot';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $hubspot = \HubSpot\Factory::createWithAccessToken(config('services.hubspot.access_token'));

            // get companies from customer view [lifecyclestage is any of customer]
            $filter = new \HubSpot\Client\Crm\Companies\Model\Filter();
            $filter
                ->setOperator('EQ')
                ->setPropertyName('lifecyclestage')
                ->setValue('customer');

            $filterGroup = new \HubSpot\Client\Crm\Companies\Model\FilterGroup();
            $filterGroup->setFilters([$filter]);

            $searchRequest = new \HubSpot\Client\Crm\Companies\Model\PublicObjectSearchRequest();
            $searchRequest->setFilterGroups([$filterGroup]);
            $searchRequest->setProperties(['name', 'domain', 'city', 'industry', 'phone', 'state', 'original_name', 'customer_id', 'email']);

            $offset = 0;
            $limit = 100;
            $customers = [];

            do {
                $searchRequest->setLimit($limit);
                $searchRequest->setAfter($offset);
                $companyPage = $hubspot->crm()->companies()->searchApi()->doSearch($searchRequest);
                $companies = $companyPage->getResults();

                foreach ($companies as $company) {
                    /**
                     * @var $customer SimplePublicObjectWithAssociations
                     */
                    $properties = $company->getProperties();
                    if ($company->getArchivedAt()) {
                        continue;
                    }

                    $id = $company->getId();
                    $name = $properties['name'];
                    $email = $properties['email'] ?? '';

                    $data = [
                        'id' => $id,
                        'hubspot_id' => $id,
                        'email' => $email,
                        'city' => $properties['city'],
                        'domain' => $properties['domain'],
                        'industry' => $properties['industry'],
                        'name' => $name,
                        'phone' => $properties['phone'] ?? '',
                        'state' => $properties['state'],
                        'original_name' => $properties['original_name'] ?? '',
                        'code' => $properties['customer_id'] ?? self::PREFIX . $id,

                        'created_at' => date('Y-m-d H:i:s', strtotime($properties['createdate'])),
                        'updated_at' => date('Y-m-d H:i:s', strtotime($properties['hs_lastmodifieddate'])),
                    ];
                    $customers[] = $data;

                    $this->info($id . ' => ' . $name . ' => ' . $email);
                }

                $hasMore = $companyPage->getPaging();

                $offset += $limit;
            } while ($hasMore);

            Customer::whereNotNull('hubspot_id')->delete();
            Customer::insert($customers);
            DB::commit();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
            var_dump($e->getTraceAsString());
        }
    }

    /**
     * @param $hubspot \HubSpot\Discovery\Discovery
     * @param $companyId
     * @return string
     */
    public function getContactEmail($hubspot, $companyId)
    {
        $contacts = $hubspot->crm()->contacts()->searchApi()
            ->doSearch([
                'filterGroups' => [
                    [
                        'filters' => [
                            [
                                'propertyName' => 'associatedcompanyid',
                                'operator' => 'EQ',
                                'value' => $companyId,
                            ],
                        ],
                    ],
                ],
            ]);

        $items = $contacts->getResults();
        if (count($items)) {
            $item = $items[0];
            $data = $item->getProperties();

            return $data['email'] ?? self::EMAIL;
        }

        return self::EMAIL;
    }
}
