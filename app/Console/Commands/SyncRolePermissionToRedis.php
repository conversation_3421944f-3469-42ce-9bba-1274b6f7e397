<?php

namespace App\Console\Commands;

use App\Enums\Enum;
use App\Enums\EPosition;
use App\Enums\ERole;
use App\Interfaces\PermissionRoleRepositoryInterface;
use App\Repositories\ProjectUserRoleRepository;
use App\Services\ProjectService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;

class SyncRolePermissionToRedis extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync role permission ro redis';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(
        PermissionRoleRepositoryInterface $permissionRoleRepository,
        ProjectService $projectService,
        ProjectUserRoleRepository $projectUserRoleRepository
    ) {
        $listUserDivision = Http::get(env('LINK_HR_SERVICE').'get-user-division')->json()['data'];
        $listUserPosition = Http::get(env('LINK_USER_SERVICE').'user-position')->json()['data'];
        $userDivisions = array_column($listUserDivision, 'division_id', 'user_id');

        if ($listUserPosition) {
            // sync data site permissions
            $rolePermission = $permissionRoleRepository
                ->getList([
                    'is_position' => 1,
                ])
                ->groupBy('position_id')
                ->map(function ($item) {
                    return $item->pluck('permission_name');
                })
                ->toArray();

            $listUserPermission = [];

            foreach ($listUserPosition as $userPosition) {
                $userId = $userPosition['user_id'];
                $positionId = $userPosition['position_id'];
                Redis::del($userId);

                $projectUserRoleRepository->destroyWithRoleDL($userId);

                if (in_array($positionId, EPosition::GROUP_DL)) {
                    $divisionId = $userDivisions[$userId];
                    $projectsOfUser = $projectService->getProjectIdsByDivision($divisionId);

                    foreach ($projectsOfUser as $projectId) {
                        $projectUserRoleRepository->addMember([
                            'user_ids' => (array)$userId,
                            'project_id' => $projectId,
                            'role_id' => $positionId == EPosition::DL ? ERole::DL : ERole::SUB_DL
                        ]);
                    }
                }

                if (isset($rolePermission[$positionId])) {
                    $permissions = $rolePermission[$positionId];
                    foreach ($permissions as $permission) {
                        $listUserPermission[$userId][] = $permission;
                    }
                }
            }

            Redis::del(Enum::PREFIX_ADMIN);

            foreach ($listUserPermission as $userId => $userPermission) {
                Redis::set($userId, json_encode($userPermission));
            }

            // sync data permissions in project
            $users = array_column($listUserPosition, 'user_id');
            $projectService->syncPermissionToRedis($users);
        }

        $this->info('Success!');

        return 0;
    }
}
