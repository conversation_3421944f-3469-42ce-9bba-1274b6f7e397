<?php

namespace App\Console\Commands;

use App\Services\AllocationService;
use App\Traits\UserData;
use Illuminate\Console\Command;

class UpdateCoefficientAllocation2023 extends Command
{
    use UserData;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:coefficient';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(AllocationService $allocationService)
    {
        $allocations = $allocationService->getAllocationIn2023();
        $userIds = $allocations->pluck('user_id');
        $users = $this->getAllUser(['user_ids'=>$userIds]);
        $allocations->each(function ($allocate) use ($users) {
            $user = $users->where('user_id', $allocate->user_id)->first();
            $allocate->coefficient = $user->level->coefficient ?? 1;
            $allocate->save();
        });
    }
}
