<?php

namespace App\Console\Commands;

use App\Models\Customer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AddCustomer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customers:create {name} {email} {phone} {address}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create customer';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $name = $this->argument('name');
            $email = $this->argument('email');
            $phone = $this->argument('phone');
            $address = $this->argument('address');

            $data = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'address' => $address,
            ];

            $validator = Validator::make($data, [
                'email' => 'email',
                'phone' => 'numeric',
            ]);
            if ($validator->fails()) {
                $this->warn('Data invalid');
                return Command::INVALID;
            }

            $customer = Customer::where('name', $name)->first();
            if (!empty($customer)) {
                $this->warn('Customer exists');
                return Command::SUCCESS;
            }

            Customer::create($data);

            $this->info('success');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            var_dump($e->getMessage());
        }
    }
}
