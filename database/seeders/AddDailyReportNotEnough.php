<?php

namespace Database\Seeders;

use App\Enums\EPermission;
use App\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AddDailyReportNotEnough extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'projects.daily-reports-enough',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 1,
            'group' => 'daily report'
        ]);
        Permission::create([
            'name' => 'projects.daily-reports-not-enough',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 1,
            'group' => 'daily report'
        ]);
    }
}
