<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AddProjectEffortPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'dashboard.resources.division.effort',
            'type' => 1,
            'order' => 3,
            'group' => 'dashboard',
        ]);

        Permission::create([
            'name' => 'dashboard.resources.project.effort',
            'type' => 1,
            'order' => 3,
            'group' => 'dashboard',
        ]);

        Permission::create([
            'name' => 'dashboard.resources.resource.effort',
            'type' => 1,
            'order' => 3,
            'group' => 'dashboard',
        ]);

        Permission::create([
            'name' => 'dashboard.resources.division.overview',
            'type' => 1,
            'order' => 3,
            'group' => 'dashboard',
        ]);
    }
}
