<?php

namespace Database\Seeders;

use App\Enums\EPermission;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class SiteRentCostPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'resource-rental-costs.index',
            'type' => EPermission::TYPE_SITE,
            'order' => 1,
            'group' => 'charge rate'
        ]);

        Permission::create([
            'name' => 'resource-rental-costs.update',
            'type' => EPermission::TYPE_SITE,
            'order' => 1,
            'group' => 'charge rate'
        ]);
    }
}
