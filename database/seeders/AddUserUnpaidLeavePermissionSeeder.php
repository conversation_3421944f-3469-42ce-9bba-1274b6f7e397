<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class AddUserUnpaidLeavePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'user-paid-leave.index',
            'type' => 1,
            'order' => 1,
            'group' => 'user paid leave'
        ]);

        Permission::create([
            'name' => 'user-paid-leave.store',
            'type' => 1,
            'order' => 1,
            'group' => 'user paid leave'
        ]);

        Permission::create([
            'name' => 'user-paid-leave.update',
            'type' => 1,
            'order' => 1,
            'group' => 'user paid leave'
        ]);

        Permission::create([
            'name' => 'user-paid-leave.destroy',
            'type' => 1,
            'order' => 1,
            'group' => 'user paid leave'
        ]);
    }
}
