<?php

namespace Database\Seeders;

use App\Models\Position;
use Illuminate\Database\Seeder;

class PositionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Position::truncate();
        Position::insert([
            [
                'name' => 'PM1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Developer',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Tester',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'PM',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Comtor',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'BrSE',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Technical Lead',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Test Lead',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'BA',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Team Lead',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Designer',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
