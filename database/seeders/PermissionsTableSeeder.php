<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class PermissionsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('permissions')->delete();
        
        \DB::table('permissions')->insert(array (
            0 => 
            array (
                'id' => 1,
                'name' => 'projects.members.index',
                'description' => '',
                'type' => 2,
                'order' => 18,
                'group' => 'project setting',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'name' => 'projects.members.store',
                'description' => '',
                'type' => 2,
                'order' => 19,
                'group' => 'project setting',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            2 => 
            array (
                'id' => 4,
                'name' => 'projects.members.destroy',
                'description' => '',
                'type' => 2,
                'order' => 20,
                'group' => 'project setting',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            3 => 
            array (
                'id' => 25,
                'name' => 'customers.store',
                'description' => '',
                'type' => 1,
                'order' => 19,
                'group' => 'customer',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            4 => 
            array (
                'id' => 27,
                'name' => 'customers.index',
                'description' => '',
                'type' => 1,
                'order' => 20,
                'group' => 'customer',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            5 => 
            array (
                'id' => 29,
                'name' => 'customers.show',
                'description' => '',
                'type' => 1,
                'order' => 21,
                'group' => 'customer',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            6 => 
            array (
                'id' => 30,
                'name' => 'customers.update',
                'description' => '',
                'type' => 1,
                'order' => 22,
                'group' => 'customer',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            7 => 
            array (
                'id' => 31,
                'name' => 'customers.destroy',
                'description' => '',
                'type' => 1,
                'order' => 23,
                'group' => 'customer',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            8 => 
            array (
                'id' => 32,
                'name' => 'revenues',
                'description' => '',
                'type' => 1,
                'order' => 15,
                'group' => 'revenue',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            9 => 
            array (
                'id' => 33,
                'name' => 'revenues.update',
                'description' => '',
                'type' => 1,
                'order' => 16,
                'group' => 'revenue',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            10 => 
            array (
                'id' => 34,
                'name' => 'revenues.show',
                'description' => '',
                'type' => 1,
                'order' => 17,
                'group' => 'revenue',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            11 => 
            array (
                'id' => 36,
                'name' => 'revenues.store',
                'description' => '',
                'type' => 1,
                'order' => 18,
                'group' => 'revenue',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            12 => 
            array (
                'id' => 41,
                'name' => 'report.weekly',
                'description' => '',
                'type' => 1,
                'order' => 26,
                'group' => 'weekly report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            13 => 
            array (
                'id' => 45,
                'name' => 'projects.index',
                'description' => '',
                'type' => 1,
                'order' => 10,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            14 => 
            array (
                'id' => 46,
                'name' => 'projects.store',
                'description' => '',
                'type' => 1,
                'order' => 11,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            15 => 
            array (
                'id' => 47,
                'name' => 'dashboard',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            16 => 
            array (
                'id' => 49,
                'name' => 'settings',
                'description' => '',
                'type' => 1,
                'order' => 24,
                'group' => 'settings',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            17 => 
            array (
                'id' => 53,
                'name' => 'projects.overview',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project overview',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            18 => 
            array (
                'id' => 56,
                'name' => 'projects.pm-report.store',
                'description' => '',
                'type' => 2,
                'order' => 7,
                'group' => 'project pm report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            19 => 
            array (
                'id' => 60,
                'name' => 'projects.requirement-change.import',
                'description' => '',
                'type' => 2,
                'order' => 25,
                'group' => 'project change request',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            20 => 
            array (
                'id' => 61,
                'name' => 'projects.requirement-change.update',
                'description' => '',
                'type' => 2,
                'order' => 21,
                'group' => 'project change request',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            21 => 
            array (
                'id' => 62,
                'name' => 'projects.requirement-change.destroy',
                'description' => '',
                'type' => 2,
                'order' => 22,
                'group' => 'project change request',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            22 => 
            array (
                'id' => 63,
                'name' => 'projects.ot',
                'description' => '',
                'type' => 2,
                'order' => 3,
                'group' => 'project overtime',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            23 => 
            array (
                'id' => 64,
                'name' => 'projects.requirement-change.index',
                'description' => '',
                'type' => 2,
                'order' => 23,
                'group' => 'project change request',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            24 => 
            array (
                'id' => 65,
                'name' => 'projects.requirement-change.store',
                'description' => '',
                'type' => 2,
                'order' => 24,
                'group' => 'project change request',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            25 => 
            array (
                'id' => 66,
                'name' => 'projects.requesting',
                'description' => '',
                'type' => 1,
                'order' => 12,
                'group' => 'requesting project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            26 => 
            array (
                'id' => 67,
                'name' => 'projects.show',
                'description' => '',
                'type' => 2,
                'order' => 54,
                'group' => 'project detail',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            27 => 
            array (
                'id' => 68,
                'name' => 'projects.update',
                'description' => '',
                'type' => 1,
                'order' => 55,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            28 => 
            array (
                'id' => 70,
                'name' => 'projects.pm-report.index',
                'description' => '',
                'type' => 2,
                'order' => 8,
                'group' => 'project pm report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            29 => 
            array (
                'id' => 71,
                'name' => 'projects.pm-report.show',
                'description' => '',
                'type' => 2,
                'order' => 9,
                'group' => 'project pm report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            30 => 
            array (
                'id' => 72,
                'name' => 'projects.pm-report.update',
                'description' => '',
                'type' => 2,
                'order' => 10,
                'group' => 'project pm report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            31 => 
            array (
                'id' => 73,
                'name' => 'projects.stages.deliverables.store',
                'description' => '',
                'type' => 2,
                'order' => 36,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            32 => 
            array (
                'id' => 74,
                'name' => 'projects.stages.deliverables.index',
                'description' => '',
                'type' => 2,
                'order' => 37,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            33 => 
            array (
                'id' => 76,
                'name' => 'projects.stages.deliverables.destroy',
                'description' => '',
                'type' => 2,
                'order' => 38,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            34 => 
            array (
                'id' => 77,
                'name' => 'projects.stages.milestones.index',
                'description' => '',
                'type' => 2,
                'order' => 30,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            35 => 
            array (
                'id' => 78,
                'name' => 'projects.stages.milestones.store',
                'description' => '',
                'type' => 2,
                'order' => 31,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            36 => 
            array (
                'id' => 79,
                'name' => 'projects.stages.milestones.show',
                'description' => '',
                'type' => 2,
                'order' => 32,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            37 => 
            array (
                'id' => 80,
                'name' => 'projects.stages.milestones.destroy',
                'description' => '',
                'type' => 2,
                'order' => 33,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            38 => 
            array (
                'id' => 81,
                'name' => 'projects.stages.allocations.index',
                'description' => '',
                'type' => 2,
                'order' => 45,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            39 => 
            array (
                'id' => 82,
                'name' => 'projects.stages.allocations.store',
                'description' => '',
                'type' => 2,
                'order' => 46,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            40 => 
            array (
                'id' => 83,
                'name' => 'projects.stages.allocations.show',
                'description' => '',
                'type' => 2,
                'order' => 47,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            41 => 
            array (
                'id' => 84,
                'name' => 'projects.stages.allocations.update',
                'description' => '',
                'type' => 2,
                'order' => 48,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            42 => 
            array (
                'id' => 85,
                'name' => 'projects.stages.allocations.destroy',
                'description' => '',
                'type' => 2,
                'order' => 49,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            43 => 
            array (
                'id' => 86,
                'name' => 'projects.stages.index',
                'description' => '',
                'type' => 2,
                'order' => 50,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            44 => 
            array (
                'id' => 87,
                'name' => 'projects.stages.store',
                'description' => '',
                'type' => 2,
                'order' => 52,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            45 => 
            array (
                'id' => 88,
                'name' => 'projects.stages.show',
                'description' => '',
                'type' => 2,
                'order' => 51,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            46 => 
            array (
                'id' => 89,
                'name' => 'projects.stages.update',
                'description' => '',
                'type' => 2,
                'order' => 53,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            47 => 
            array (
                'id' => 90,
                'name' => 'projects.stages.destroy',
                'description' => '',
                'type' => 2,
                'order' => 39,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            48 => 
            array (
                'id' => 96,
                'name' => 'master_data',
                'description' => '',
                'type' => 1,
                'order' => 25,
                'group' => 'master data',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            49 => 
            array (
                'id' => 97,
                'name' => 'dashboard.resources',
                'description' => '',
                'type' => 1,
                'order' => 2,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            50 => 
            array (
                'id' => 98,
                'name' => 'dashboard.resources.man.month.position',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            51 => 
            array (
                'id' => 99,
                'name' => 'dashboard.resources.man.month.project.type',
                'description' => '',
                'type' => 1,
                'order' => 4,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            52 => 
            array (
                'id' => 100,
                'name' => 'dashboard.resources.man.month.skill',
                'description' => '',
                'type' => 1,
                'order' => 5,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            53 => 
            array (
                'id' => 101,
                'name' => 'dashboard.resources.busy.rate',
                'description' => '',
                'type' => 1,
                'order' => 6,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            54 => 
            array (
                'id' => 102,
                'name' => 'dashboard.resources.busy.rate.division',
                'description' => '',
                'type' => 1,
                'order' => 7,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            55 => 
            array (
                'id' => 103,
                'name' => 'dashboard.resources.index',
                'description' => '',
                'type' => 1,
                'order' => 8,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            56 => 
            array (
                'id' => 104,
                'name' => 'dashboard.resources.statistical',
                'description' => '',
                'type' => 1,
                'order' => 9,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            57 => 
            array (
                'id' => 105,
                'name' => 'projects.log-activities',
                'description' => '',
                'type' => 2,
                'order' => 17,
                'group' => 'project activities log',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            58 => 
            array (
                'id' => 106,
                'name' => 'projects.timeline.resource',
                'description' => '',
                'type' => 2,
                'order' => 3,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            59 => 
            array (
                'id' => 108,
                'name' => 'projects.ot.in.week',
                'description' => '',
                'type' => 2,
                'order' => 4,
                'group' => 'project overtime',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            60 => 
            array (
                'id' => 109,
                'name' => 'projects.ot.index',
                'description' => '',
                'type' => 2,
                'order' => 5,
                'group' => 'project overtime',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            61 => 
            array (
                'id' => 111,
                'name' => 'projects.overview.schedule',
                'description' => '',
                'type' => 2,
                'order' => 2,
                'group' => 'project overview',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            62 => 
            array (
                'id' => 112,
                'name' => 'projects.ot.total',
                'description' => '',
                'type' => 2,
                'order' => 6,
                'group' => 'project overtime',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            63 => 
            array (
                'id' => 113,
                'name' => 'projects.stages.milestones.update',
                'description' => '',
                'type' => 2,
                'order' => 34,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            64 => 
            array (
                'id' => 114,
                'name' => 'projects.stages.deliverables.update',
                'description' => '',
                'type' => 2,
                'order' => 40,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            65 => 
            array (
                'id' => 115,
                'name' => 'projects.pcv-reports.index',
                'description' => '',
                'type' => 1,
                'order' => 30,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            66 => 
            array (
                'id' => 116,
                'name' => 'projects.pcv-reports.destroy',
                'description' => '',
                'type' => 1,
                'order' => 31,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            67 => 
            array (
                'id' => 117,
                'name' => 'projects.pcv-reports.store',
                'description' => '',
                'type' => 1,
                'order' => 32,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            68 => 
            array (
                'id' => 118,
                'name' => 'projects.pcv-reports.update',
                'description' => '',
                'type' => 1,
                'order' => 33,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            69 => 
            array (
                'id' => 122,
                'name' => 'projects.show',
                'description' => '',
                'type' => 1,
                'order' => 54,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            70 => 
            array (
                'id' => 123,
                'name' => 'projects.destroy',
                'description' => '',
                'type' => 1,
                'order' => 99,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            71 => 
            array (
                'id' => 124,
                'name' => 'projects.overview.index',
                'description' => '',
                'type' => 2,
                'order' => NULL,
                'group' => '',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            72 => 
            array (
                'id' => 125,
                'name' => 'projects.overview.index',
                'description' => '',
                'type' => 2,
                'order' => NULL,
                'group' => 'project overview',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            73 => 
            array (
                'id' => 126,
                'name' => 'projects.request-project-dl',
                'description' => '',
                'type' => 2,
                'order' => 99,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            74 => 
            array (
                'id' => 127,
                'name' => 'customers.sync',
                'description' => NULL,
                'type' => 1,
                'order' => NULL,
                'group' => 'customer',
                'created_at' => '2023-05-23 11:29:03',
                'updated_at' => '2023-05-23 11:29:03',
            ),
            75 => 
            array (
                'id' => 128,
                'name' => 'projects.update',
                'description' => '',
                'type' => 2,
                'order' => 1000,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            76 => 
            array (
                'id' => 129,
                'name' => 'projects.destroy',
                'description' => '',
                'type' => 2,
                'order' => 1111,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            77 => 
            array (
                'id' => 130,
                'name' => 'change-project-type',
                'description' => NULL,
                'type' => 2,
                'order' => 1112,
                'group' => 'project',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            78 => 
            array (
                'id' => 131,
                'name' => 'projects.pcv-reports.index',
                'description' => '',
                'type' => 2,
                'order' => 30,
                'group' => 'pcv report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            79 => 
            array (
                'id' => 132,
                'name' => 'projects.pcv-reports.destroy',
                'description' => '',
                'type' => 2,
                'order' => 31,
                'group' => 'pcv report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            80 => 
            array (
                'id' => 133,
                'name' => 'projects.pcv-reports.store',
                'description' => '',
                'type' => 2,
                'order' => 32,
                'group' => 'pcv report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            81 => 
            array (
                'id' => 134,
                'name' => 'projects.pcv-reports.update',
                'description' => '',
                'type' => 2,
                'order' => 33,
                'group' => 'pcv report',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            82 => 
            array (
                'id' => 138,
                'name' => 'dashboard.resources.busy.rate.month',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            83 => 
            array (
                'id' => 139,
                'name' => 'dashboard.resources.export-resource-allocation',
                'description' => NULL,
                'type' => 1,
                'order' => NULL,
                'group' => 'dashboard',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            84 => 
            array (
                'id' => 140,
                'name' => 'projects.stages.resource-allocation',
                'description' => NULL,
                'type' => 2,
                'order' => 1,
                'group' => 'project timeline resource',
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            85 => 
            array (
                'id' => 141,
                'name' => 'user-paid-leave.index',
                'description' => NULL,
                'type' => 1,
                'order' => 1,
                'group' => 'user paid leave',
                'created_at' => '2023-06-01 17:05:25',
                'updated_at' => '2023-06-01 17:05:25',
            ),
            86 => 
            array (
                'id' => 142,
                'name' => 'user-paid-leave.store',
                'description' => NULL,
                'type' => 1,
                'order' => 1,
                'group' => 'user paid leave',
                'created_at' => '2023-06-01 17:05:26',
                'updated_at' => '2023-06-01 17:05:26',
            ),
            87 => 
            array (
                'id' => 143,
                'name' => 'user-paid-leave.update',
                'description' => NULL,
                'type' => 1,
                'order' => 1,
                'group' => 'user paid leave',
                'created_at' => '2023-06-01 17:05:26',
                'updated_at' => '2023-06-01 17:05:26',
            ),
            88 => 
            array (
                'id' => 144,
                'name' => 'user-paid-leave.destroy',
                'description' => NULL,
                'type' => 1,
                'order' => 1,
                'group' => 'user paid leave',
                'created_at' => '2023-06-01 17:05:26',
                'updated_at' => '2023-06-01 17:05:26',
            ),
        ));
        
        
    }
}