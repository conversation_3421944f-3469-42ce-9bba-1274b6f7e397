<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Enums\EPermission;
use Illuminate\Database\Seeder;

class BudgetByYearPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'projects.budget-by-year.detail',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 1,
            'group' => 'project'
        ]);
        Permission::create([
            'name' => 'projects.budget-by-year.update',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 1,
            'group' => 'project'
        ]);
    }
}
