<?php

namespace Database\Seeders;

use App\Enums\EPermission;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class PenaltyRegulationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'penalty-regulations',
            'type' => EPermission::TYPE_SITE,
            'order' => 1,
            'group' => 'penalty regulations'
        ]);

        Permission::create([
            'name' => 'penalty-regulations.index',
            'type' => EPermission::TYPE_SITE,
            'order' => 2,
            'group' => 'penalty regulations'
        ]);

        Permission::create([
            'name' => 'penalty-regulations.edit',
            'type' => EPermission::TYPE_SITE,
            'order' => 3,
            'group' => 'penalty regulations'
        ]);

        Permission::create([
            'name' => 'penalty-regulations.activity-log',
            'type' => EPermission::TYPE_SITE,
            'order' => 4,
            'group' => 'penalty regulations'
        ]);

        Permission::create([
            'name' => 'penalty-regulations.master-data',
            'type' => EPermission::TYPE_SITE,
            'order' => 5,
            'group' => 'penalty regulations'
        ]);
    }
}
