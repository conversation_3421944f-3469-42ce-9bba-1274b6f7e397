<?php

namespace Database\Seeders;

use App\Enums\EPermission;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionDailyReportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'projects.daily-reports',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 1,
            'group' => 'daily report'
        ]);

        Permission::create([
            'name' => 'projects.daily-reports.export',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 2,
            'group' => 'daily report'
        ]);

        Permission::create([
            'name' => 'projects.daily-reports.change-status',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 3,
            'group' => 'daily report'
        ]);
    }
}
