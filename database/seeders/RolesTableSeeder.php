<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class RolesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {


        \DB::table('roles')->delete();

        \DB::table('roles')->insert(array(
            0 =>
            array(
                'id' => 1,
                'name' => 'BoD',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            2 =>
            array(
                'id' => 3,
                'name' => 'PM',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            3 =>
            array(
                'id' => 4,
                'name' => 'Developer ',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => '2023-03-21 15:17:23',
            ),
            4 =>
            array(
                'id' => 5,
                'name' => 'BA',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            5 =>
            array(
                'id' => 6,
                'name' => 'Tech Lead',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            6 =>
            array(
                'id' => 7,
                'name' => 'Test Lead',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            7 =>
            array(
                'id' => 8,
                'name' => 'Comtor',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            8 =>
            array(
                'id' => 9,
                'name' => 'BrSE',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            9 =>
            array(
                'id' => 10,
                'name' => 'Tester',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            10 =>
            array(
                'id' => 11,
                'name' => 'PQA',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            11 =>
            array(
                'id' => 12,
                'name' => 'Designer',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            12 =>
            array(
                'id' => 13,
                'name' => 'DL',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            13 =>
            array(
                'id' => 14,
                'name' => 'Sub-DL',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            14 =>
            array(
                'id' => 15,
                'name' => 'Sub-PM',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            15 =>
            array(
                'id' => 16,
                'name' => 'Scrum Master',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            16 =>
            array(
                'id' => 17,
                'name' => 'Amoeba Leader',
                'status' => 1,
                'description' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            )
        ));
    }
}
