<?php

namespace Database\Seeders;

use App\Enums\EProjectType;
use App\Models\Project;
use Illuminate\Database\Seeder;

class ProjectsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        Project::whereIn('code', EProjectType::PROJECT_FIXED)->forceDelete();

        Project::insert([
            'name' => 'Training Project',
            'code' => EProjectType::TRAINING_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => 'Company Event Project',
            'code' => EProjectType::COMPANY_EVENT_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => 'Company Work',
            'code' => EProjectType::COMPANY_WORK,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => '[HADES] Division work',
            'code' => EProjectType::HADES_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => '[THOR] Division work',
            'code' => EProjectType::THOR_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => '[PHOENIX] Division work',
            'code' => EProjectType::PHOENIX_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => '[BONNE] Division work',
            'code' => EProjectType::BONNE_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => '[RND] Division work',
            'code' => EProjectType::RND_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => '[SOLUTION] Division work',
            'code' => EProjectType::SOLUTION_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);

        Project::insert([
            'name' => '[FADERLESS] Division work',
            'code' => EProjectType::FADERLESS_PROJECT,
            'status' => '1',
            'division_id' => 0,
            'contract_type' => '1',
            'project_type' => '3',
            'billable' => 0.0,
            'budget' => 0.0,
            'rank' => 'D',
            'customer_type' => 7,
            'industry' => NULL,
            'language' => NULL,
            'scope' => NULL,
            'communication' => NULL,
            'description' => NULL,
            'contract_information' => NULL,
            'critical' => NULL,
            'note' => NULL,
            'start_date' => NULL,
            'end_date' => NULL,
            'created_at' => '2023-06-23 15:46:12',
            'updated_at' => '2023-06-23 15:46:12',
            'deleted_at' => NULL,
        ]);
    }
}
