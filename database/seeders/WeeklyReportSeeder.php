<?php

namespace Database\Seeders;

use App\Enums\EPermission;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class WeeklyReportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'weekly-reports',
            'type' => EPermission::TYPE_SITE,
            'order' => 1,
            'group' => 'weekly reports'
        ]);

        Permission::create([
            'name' => 'weekly-reports.index',
            'type' => EPermission::TYPE_SITE,
            'order' => 2,
            'group' => 'weekly reports'
        ]);

        Permission::create([
            'name' => 'weekly-reports.export',
            'type' => EPermission::TYPE_SITE,
            'order' => 3,
            'group' => 'weekly reports'
        ]);
    }
}
