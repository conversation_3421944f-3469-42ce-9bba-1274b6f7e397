<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            CustomerSeeder::class,
            ProjectSeeder::class,
            TechnologySeeder::class,
            CustomerProjectTableSeeder::class,
            PmProjectTableSeeder::class,
            PqaProjectTableSeeder::class,
            ProjectRoleUserTableSeeder::class,
            ProjectSellerTableSeeder::class,
            ProjectTechnologyTableSeeder::class,
            RolesTableSeeder::class,
            ProjectsTableSeeder::class
        ]);
    }
}
