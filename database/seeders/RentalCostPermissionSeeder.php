<?php

namespace Database\Seeders;

use App\Enums\EPermission;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class RentalCostPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name' => 'projects.resource-rental-costs.index',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 1,
            'group' => 'project timeline resource'
        ]);

        Permission::create([
            'name' => 'projects.resource-rental-costs.store',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 1,
            'group' => 'project timeline resource'
        ]);

        Permission::create([
            'name' => 'projects.resource-rental-costs.destroy',
            'type' => EPermission::TYPE_IN_PROJECT,
            'order' => 1,
            'group' => 'project timeline resource'
        ]);
    }
}
