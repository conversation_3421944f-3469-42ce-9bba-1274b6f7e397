<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        $permissions = [
            [
                'id' => 1,
                'name' => 'projects.members.index',
                'description' => '',
                'type' => 2,
                'order' => 18,
                'group' => 'project setting',
            ],
            [
                'id' => 2,
                'name' => 'projects.members.store',
                'description' => '',
                'type' => 2,
                'order' => 19,
                'group' => 'project setting',
            ],
            [
                'id' => 4,
                'name' => 'projects.members.destroy',
                'description' => '',
                'type' => 2,
                'order' => 20,
                'group' => 'project setting',
            ],
            [
                'id' => 25,
                'name' => 'customers.store',
                'description' => '',
                'type' => 1,
                'order' => 19,
                'group' => 'customer',
            ],
            [
                'id' => 27,
                'name' => 'customers.index',
                'description' => '',
                'type' => 1,
                'order' => 20,
                'group' => 'customer',
            ],
            [
                'id' => 29,
                'name' => 'customers.show',
                'description' => '',
                'type' => 1,
                'order' => 21,
                'group' => 'customer',
            ],
            [
                'id' => 30,
                'name' => 'customers.update',
                'description' => '',
                'type' => 1,
                'order' => 22,
                'group' => 'customer',
            ],
            [
                'id' => 31,
                'name' => 'customers.destroy',
                'description' => '',
                'type' => 1,
                'order' => 23,
                'group' => 'customer',
            ],
            [
                'id' => 32,
                'name' => 'revenues',
                'description' => '',
                'type' => 1,
                'order' => 15,
                'group' => 'revenue',
            ],
            [
                'id' => 33,
                'name' => 'revenues.update',
                'description' => '',
                'type' => 1,
                'order' => 16,
                'group' => 'revenue',
            ],
            [
                'id' => 34,
                'name' => 'revenues.show',
                'description' => '',
                'type' => 1,
                'order' => 17,
                'group' => 'revenue',
            ],
            [
                'id' => 36,
                'name' => 'revenues.store',
                'description' => '',
                'type' => 1,
                'order' => 18,
                'group' => 'revenue',
            ],
            [
                'id' => 41,
                'name' => 'report.weekly',
                'description' => '',
                'type' => 1,
                'order' => 26,
                'group' => 'weekly report',
            ],
            [
                'id' => 45,
                'name' => 'projects.index',
                'description' => '',
                'type' => 1,
                'order' => 10,
                'group' => 'project',
            ],
            [
                'id' => 46,
                'name' => 'projects.store',
                'description' => '',
                'type' => 1,
                'order' => 11,
                'group' => 'project',
            ],
            [
                'id' => 47,
                'name' => 'dashboard',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'dashboard',
            ],
            [
                'id' => 49,
                'name' => 'settings',
                'description' => '',
                'type' => 1,
                'order' => 24,
                'group' => 'settings',
            ],
            [
                'id' => 53,
                'name' => 'projects.overview',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project overview',
            ],
            [
                'id' => 56,
                'name' => 'projects.pm-report.store',
                'description' => '',
                'type' => 2,
                'order' => 7,
                'group' => 'project pm report',
            ],
            [
                'id' => 60,
                'name' => 'projects.requirement-change.import',
                'description' => '',
                'type' => 2,
                'order' => 25,
                'group' => 'project change request',
            ],
            [
                'id' => 61,
                'name' => 'projects.requirement-change.update',
                'description' => '',
                'type' => 2,
                'order' => 21,
                'group' => 'project change request',
            ],
            [
                'id' => 62,
                'name' => 'projects.requirement-change.destroy',
                'description' => '',
                'type' => 2,
                'order' => 22,
                'group' => 'project change request',
            ],
            [
                'id' => 63,
                'name' => 'projects.ot',
                'description' => '',
                'type' => 2,
                'order' => 3,
                'group' => 'project overtime',
            ],
            [
                'id' => 64,
                'name' => 'projects.requirement-change.index',
                'description' => '',
                'type' => 2,
                'order' => 23,
                'group' => 'project change request',
            ],
            [
                'id' => 65,
                'name' => 'projects.requirement-change.store',
                'description' => '',
                'type' => 2,
                'order' => 24,
                'group' => 'project change request',
            ],
            [
                'id' => 66,
                'name' => 'projects.requesting',
                'description' => '',
                'type' => 1,
                'order' => 12,
                'group' => 'requesting project',
            ],
            [
                'id' => 67,
                'name' => 'projects.show',
                'description' => '',
                'type' => 2,
                'order' => 54,
                'group' => 'project detail',
            ],
            [
                'id' => 68,
                'name' => 'projects.update',
                'description' => '',
                'type' => 1,
                'order' => 55,
                'group' => 'project',
            ],
            [
                'id' => 70,
                'name' => 'projects.pm-report.index',
                'description' => '',
                'type' => 2,
                'order' => 8,
                'group' => 'project pm report',
            ],
            [
                'id' => 71,
                'name' => 'projects.pm-report.show',
                'description' => '',
                'type' => 2,
                'order' => 9,
                'group' => 'project pm report',
            ],
            [
                'id' => 72,
                'name' => 'projects.pm-report.update',
                'description' => '',
                'type' => 2,
                'order' => 10,
                'group' => 'project pm report',
            ],
            [
                'id' => 73,
                'name' => 'projects.stages.deliverables.store',
                'description' => '',
                'type' => 2,
                'order' => 36,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 74,
                'name' => 'projects.stages.deliverables.index',
                'description' => '',
                'type' => 2,
                'order' => 37,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 76,
                'name' => 'projects.stages.deliverables.destroy',
                'description' => '',
                'type' => 2,
                'order' => 38,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 77,
                'name' => 'projects.stages.milestones.index',
                'description' => '',
                'type' => 2,
                'order' => 30,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 78,
                'name' => 'projects.stages.milestones.store',
                'description' => '',
                'type' => 2,
                'order' => 31,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 79,
                'name' => 'projects.stages.milestones.show',
                'description' => '',
                'type' => 2,
                'order' => 32,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 80,
                'name' => 'projects.stages.milestones.destroy',
                'description' => '',
                'type' => 2,
                'order' => 33,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 81,
                'name' => 'projects.stages.allocations.index',
                'description' => '',
                'type' => 2,
                'order' => 45,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 82,
                'name' => 'projects.stages.allocations.store',
                'description' => '',
                'type' => 2,
                'order' => 46,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 83,
                'name' => 'projects.stages.allocations.show',
                'description' => '',
                'type' => 2,
                'order' => 47,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 84,
                'name' => 'projects.stages.allocations.update',
                'description' => '',
                'type' => 2,
                'order' => 48,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 85,
                'name' => 'projects.stages.allocations.destroy',
                'description' => '',
                'type' => 2,
                'order' => 49,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 86,
                'name' => 'projects.stages.index',
                'description' => '',
                'type' => 2,
                'order' => 50,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 87,
                'name' => 'projects.stages.store',
                'description' => '',
                'type' => 2,
                'order' => 52,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 88,
                'name' => 'projects.stages.show',
                'description' => '',
                'type' => 2,
                'order' => 51,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 89,
                'name' => 'projects.stages.update',
                'description' => '',
                'type' => 2,
                'order' => 53,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 90,
                'name' => 'projects.stages.destroy',
                'description' => '',
                'type' => 2,
                'order' => 39,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 96,
                'name' => 'master_data',
                'description' => '',
                'type' => 1,
                'order' => 25,
                'group' => 'master data',
            ],
            [
                'id' => 97,
                'name' => 'dashboard.resources',
                'description' => '',
                'type' => 1,
                'order' => 2,
                'group' => 'dashboard',
            ],
            [
                'id' => 98,
                'name' => 'dashboard.resources.man.month.position',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 99,
                'name' => 'dashboard.resources.man.month.project.type',
                'description' => '',
                'type' => 1,
                'order' => 4,
                'group' => 'dashboard',
            ],
            [
                'id' => 100,
                'name' => 'dashboard.resources.man.month.skill',
                'description' => '',
                'type' => 1,
                'order' => 5,
                'group' => 'dashboard',
            ],
            [
                'id' => 101,
                'name' => 'dashboard.resources.busy.rate',
                'description' => '',
                'type' => 1,
                'order' => 6,
                'group' => 'dashboard',
            ],
            [
                'id' => 102,
                'name' => 'dashboard.resources.busy.rate.division',
                'description' => '',
                'type' => 1,
                'order' => 7,
                'group' => 'dashboard',
            ],
            [
                'id' => 103,
                'name' => 'dashboard.resources.index',
                'description' => '',
                'type' => 1,
                'order' => 8,
                'group' => 'dashboard',
            ],
            [
                'id' => 104,
                'name' => 'dashboard.resources.statistical',
                'description' => '',
                'type' => 1,
                'order' => 9,
                'group' => 'dashboard',
            ],
            [
                'id' => 105,
                'name' => 'projects.log-activities',
                'description' => '',
                'type' => 2,
                'order' => 17,
                'group' => 'project activities log',
            ],
            [
                'id' => 106,
                'name' => 'projects.timeline.resource',
                'description' => '',
                'type' => 2,
                'order' => 3,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 108,
                'name' => 'projects.ot.in.week',
                'description' => '',
                'type' => 2,
                'order' => 4,
                'group' => 'project overtime',
            ],
            [
                'id' => 109,
                'name' => 'projects.ot.index',
                'description' => '',
                'type' => 2,
                'order' => 5,
                'group' => 'project overtime',
            ],
            [
                'id' => 111,
                'name' => 'projects.overview.schedule',
                'description' => '',
                'type' => 2,
                'order' => 2,
                'group' => 'project overview',
            ],
            [
                'id' => 112,
                'name' => 'projects.ot.total',
                'description' => '',
                'type' => 2,
                'order' => 6,
                'group' => 'project overtime',
            ],
            [
                'id' => 113,
                'name' => 'projects.stages.milestones.update',
                'description' => '',
                'type' => 2,
                'order' => 34,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 114,
                'name' => 'projects.stages.deliverables.update',
                'description' => '',
                'type' => 2,
                'order' => 40,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 115,
                'name' => 'projects.pcv-reports.index',
                'description' => '',
                'type' => 1,
                'order' => 30,
                'group' => 'project',
            ],
            [
                'id' => 116,
                'name' => 'projects.pcv-reports.destroy',
                'description' => '',
                'type' => 1,
                'order' => 31,
                'group' => 'project',
            ],
            [
                'id' => 117,
                'name' => 'projects.pcv-reports.store',
                'description' => '',
                'type' => 1,
                'order' => 32,
                'group' => 'project',
            ],
            [
                'id' => 118,
                'name' => 'projects.pcv-reports.update',
                'description' => '',
                'type' => 1,
                'order' => 33,
                'group' => 'project',
            ],
            [
                'id' => 122,
                'name' => 'projects.show',
                'description' => '',
                'type' => 1,
                'order' => 54,
                'group' => 'project',
            ],
            [
                'id' => 123,
                'name' => 'projects.destroy',
                'description' => '',
                'type' => 1,
                'order' => 99,
                'group' => 'project',
            ],
            [
                'id' => 124,
                'name' => 'projects.overview.index',
                'description' => '',
                'type' => 2,
                'order' => NULL,
                'group' => '',
            ],
            [
                'id' => 125,
                'name' => 'projects.overview.index',
                'description' => '',
                'type' => 2,
                'order' => NULL,
                'group' => 'project overview',
            ],
            [
                'id' => 126,
                'name' => 'projects.request-project-dl',
                'description' => '',
                'type' => 2,
                'order' => 99,
                'group' => 'project',
            ],
            [
                'id' => 127,
                'name' => 'customers.sync',
                'description' => '',
                'type' => 1,
                'order' => NULL,
                'group' => 'customer',
            ],
            [
                'id' => 128,
                'name' => 'projects.update',
                'description' => '',
                'type' => 2,
                'order' => 1000,
                'group' => 'project',
            ],
            [
                'id' => 129,
                'name' => 'projects.destroy',
                'description' => '',
                'type' => 2,
                'order' => 1111,
                'group' => 'project',
            ],
            [
                'id' => 130,
                'name' => 'change-project-type',
                'description' => '',
                'type' => 2,
                'order' => 1112,
                'group' => 'project',
            ],
            [
                'id' => 131,
                'name' => 'projects.pcv-reports.index',
                'description' => '',
                'type' => 2,
                'order' => 30,
                'group' => 'pcv report',
            ],
            [
                'id' => 132,
                'name' => 'projects.pcv-reports.destroy',
                'description' => '',
                'type' => 2,
                'order' => 31,
                'group' => 'pcv report',
            ],
            [
                'id' => 133,
                'name' => 'projects.pcv-reports.store',
                'description' => '',
                'type' => 2,
                'order' => 32,
                'group' => 'pcv report',
            ],
            [
                'id' => 134,
                'name' => 'projects.pcv-reports.update',
                'description' => '',
                'type' => 2,
                'order' => 33,
                'group' => 'pcv report',
            ],
            [
                'id' => 138,
                'name' => 'dashboard.resources.busy.rate.month',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'dashboard',
            ],
            [
                'id' => 139,
                'name' => 'dashboard.resources.export-resource-allocation',
                'description' => '',
                'type' => 1,
                'order' => NULL,
                'group' => 'dashboard',
            ],
            [
                'id' => 140,
                'name' => 'projects.stages.resource-allocation',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 141,
                'name' => 'user-paid-leave.index',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'user paid leave',
            ],
            [
                'id' => 142,
                'name' => 'user-paid-leave.store',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'user paid leave',
            ],
            [
                'id' => 143,
                'name' => 'user-paid-leave.update',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'user paid leave',
            ],
            [
                'id' => 144,
                'name' => 'user-paid-leave.destroy',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'user paid leave',
            ],
            [
                'id' => 145,
                'name' => 'projects.daily-reports',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'daily report',
            ],
            [
                'id' => 146,
                'name' => 'projects.daily-reports.export',
                'description' => '',
                'type' => 2,
                'order' => 2,
                'group' => 'daily report',
            ],
            [
                'id' => 147,
                'name' => 'projects.daily-reports.change-status',
                'description' => '',
                'type' => 2,
                'order' => 3,
                'group' => 'daily report',
            ],
            [
                'id' => 148,
                'name' => 'projects.ee-chart',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project',
            ],
            [
                'id' => 149,
                'name' => 'projects.budget-by-year.detail',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project',
            ],
            [
                'id' => 150,
                'name' => 'projects.budget-by-year.update',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project',
            ],
            [
                'id' => 151,
                'name' => 'project.statistics',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'log work',
            ],
            [
                'id' => 152,
                'name' => 'dashboard.resources.division.effort',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 153,
                'name' => 'dashboard.resources.project.effort',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 154,
                'name' => 'dashboard.resources.division.effort',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 155,
                'name' => 'dashboard.resources.project.effort',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 156,
                'name' => 'dashboard.resources.resource.effort',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 157,
                'name' => 'dashboard.resources.division.overview',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 158,
                'name' => 'project.budget.statistics',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'charge rate',
            ],
            [
                'id' => 159,
                'name' => 'projects.stages.clone',
                'description' => '',
                'type' => 2,
                'order' => 52,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 160,
                'name' => 'division-charge-rate-statistics-chart',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'charge rate',
            ],
            [
                'id' => 161,
                'name' => 'division-charge-rate-statistics',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'charge rate',
            ],
            [
                'id' => 162,
                'name' => 'log-work-division-filter',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'log work',
            ],
            [
                'id' => 163,
                'name' => 'dashboard.resources.calendar.effort',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 164,
                'name' => 'projects.resource-rental-costs.index',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 165,
                'name' => 'projects.resource-rental-costs.store',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 166,
                'name' => 'projects.resource-rental-costs.destroy',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project timeline resource',
            ],
            [
                'id' => 167,
                'name' => 'resource-rental-costs.index',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'charge rate',
            ],
            [
                'id' => 168,
                'name' => 'resource-rental-costs.update',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'charge rate',
            ],
            [
                'id' => 169,
                'name' => 'budget-by-year.create',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'charge rate',
            ],
            [
                'id' => 170,
                'name' => 'projects.daily-reports-enough',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'daily report',
            ],
            [
                'id' => 171,
                'name' => 'projects.daily-reports-not-enough',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'daily report',
            ],
            [
                'id' => 172,
                'name' => 'dashboard.resources.export.log-work',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'dashboard',
            ],
            [
                'id' => 173,
                'name' => 'penalty-regulations',
                'description' => '',
                'type' => 1,
                'order' => 1,
                'group' => 'penalty regulations',
            ],
            [
                'id' => 174,
                'name' => 'penalty-regulations.index',
                'description' => '',
                'type' => 1,
                'order' => 2,
                'group' => 'penalty regulations',
            ],
            [
                'id' => 175,
                'name' => 'penalty-regulations.edit',
                'description' => '',
                'type' => 1,
                'order' => 3,
                'group' => 'penalty regulations',
            ],
            [
                'id' => 176,
                'name' => 'penalty-regulations.activity-log',
                'description' => '',
                'type' => 1,
                'order' => 4,
                'group' => 'penalty regulations',
            ],
            [
                'id' => 177,
                'name' => 'penalty-regulations.master-data',
                'description' => '',
                'type' => 1,
                'order' => 5,
                'group' => 'penalty regulations',
            ],
            [
                'id' => 178,
                'name' => 'projects.performance.index',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project performance',
            ],
            [
                'id' => 179,
                'name' => 'projects.performance.dashboard',
                'description' => '',
                'type' => 2,
                'order' => 2,
                'group' => 'project performance',
            ],
            [
                'id' => 180,
                'name' => 'projects.performance.master_data',
                'description' => '',
                'type' => 2,
                'order' => 3,
                'group' => 'project performance',
            ],
            [
                'id' => 181,
                'name' => 'projects.performance.sync_data',
                'description' => '',
                'type' => 2,
                'order' => 4,
                'group' => 'project performance',
            ],
            [
                'id' => 182,
                'name' => 'projects.performance.pqa_quality_gate.index',
                'description' => '',
                'type' => 2,
                'order' => 5,
                'group' => 'project performance',
            ],
            [
                'id' => 183,
                'name' => 'projects.performance.pqa_quality_gate.create',
                'description' => '',
                'type' => 2,
                'order' => 6,
                'group' => 'project performance',
            ],
            [
                'id' => 184,
                'name' => 'projects.performance.pqa_quality_gate.update',
                'description' => '',
                'type' => 2,
                'order' => 7,
                'group' => 'project performance',
            ],
            [
                'id' => 185,
                'name' => 'projects.performance.pqa_quality_gate.delete',
                'description' => '',
                'type' => 2,
                'order' => 8,
                'group' => 'project performance',
            ],
            [
                'id' => 186,
                'name' => 'projects.function.index',
                'description' => '',
                'type' => 2,
                'order' => 1,
                'group' => 'project function',
            ],
            [
                'id' => 187,
                'name' => 'projects.function.create',
                'description' => '',
                'type' => 2,
                'order' => 2,
                'group' => 'project function',
            ],
            [
                'id' => 188,
                'name' => 'projects.function.update',
                'description' => '',
                'type' => 2,
                'order' => 3,
                'group' => 'project function',
            ],
            [
                'id' => 189,
                'name' => 'projects.function.delete',
                'description' => '',
                'type' => 2,
                'order' => 4,
                'group' => 'project function',
            ],
        ];

        Permission::query()->upsert($permissions, 'id');
    }
}
