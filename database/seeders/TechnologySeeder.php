<?php

namespace Database\Seeders;

use App\Models\Technology;
use Illuminate\Database\Seeder;

class TechnologySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Technology::truncate();

        Technology::insert([
            [
                'programing_language' => 'PHP',
                'framework' => 'Laravel',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'programing_language' => 'Javascript',
                'framework' => 'NodeJs',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'programing_language' => 'Javascript',
                'framework' => 'NestJs',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'programing_language' => 'Javascript',
                'framework' => 'ReactJs',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'programing_language' => 'Javascript',
                'framework' => 'NextJs',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'programing_language' => 'Javascript',
                'framework' => 'VueJs',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'programing_language' => 'PHP',
                'framework' => 'CakePHP',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
