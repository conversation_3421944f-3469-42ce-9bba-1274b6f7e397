<?php

use App\Enums\EFileEvidenceType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('file_evidence', function (Blueprint $table) {
            $table->id();
            $table->integer('requirement_change_id');
            $table->string('url');
            $table->enum('file_type', EFileEvidenceType::getAll())->comment('1: Evidence of customer closing CR, 2: Evidence finished CR');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('file_evidence');
    }
};
