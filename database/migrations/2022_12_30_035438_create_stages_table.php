<?php

use App\Enums\EStageStatus;
use App\Enums\EStageType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stages', function (Blueprint $table) {
            $table->id();
            $table->integer('project_id');
            $table->enum('type', EStageType::getAll())->comment('1:sprint, 2:labo, 3:solution, 4:construction, 5:transition and termination, 6:guarantee')->default(1);
            $table->string('version')->nullable();
            $table->enum('status', EStageStatus::getAll())->comment('1:open, 2:in progress, 3:close')->default(1);
            $table->float('billable');
            $table->float('budget');
            $table->date('start_date');
            $table->date('end_date');
            $table->timestamps();
            $table->index(['project_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stages');
    }
};
