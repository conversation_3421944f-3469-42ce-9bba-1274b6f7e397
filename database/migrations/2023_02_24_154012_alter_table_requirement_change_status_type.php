<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \DB::statement("ALTER TABLE `requirement_change` CHANGE `status` `status` ENUM('1','2','3','4','5') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;");
        \DB::statement("ALTER TABLE `requirement_change` CHANGE `type` `type` ENUM('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;");
        \DB::statement("ALTER TABLE `requirement_change` CHANGE `priority` `priority` ENUM('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \DB::statement("ALTER TABLE `requirement_change` CHANGE `status` `status` enum('1','2','3','4','5') COLLATE 'utf8mb4_unicode_ci' NOT NULL;");
        \DB::statement("ALTER TABLE `requirement_change` CHANGE `type` `type` enum('1','2') COLLATE 'utf8mb4_unicode_ci' NOT NULL;");
        \DB::statement("ALTER TABLE `requirement_change` CHANGE `priority` `priority` enum('1','2') COLLATE 'utf8mb4_unicode_ci' NOT NULL;");
    }
};
