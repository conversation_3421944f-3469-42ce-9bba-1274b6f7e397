<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE `pm_reports`
CHANGE `cost_status` `cost_status` enum('good','warning','serious','na') COLLATE 'utf8mb4_unicode_ci' NOT NULL COMMENT '1: good, 2: warning, 3: serious' AFTER `template_id`;");

        DB::statement("ALTER TABLE `pm_reports`
CHANGE `quality_status` `quality_status` enum('good','warning','serious','na') COLLATE 'utf8mb4_unicode_ci' NOT NULL COMMENT '1: good, 2: warning, 3: serious' AFTER `template_id`;");

        DB::statement("ALTER TABLE `pm_reports`
CHANGE `timeliness_status` `timeliness_status` enum('good','warning','serious','na') COLLATE 'utf8mb4_unicode_ci' NOT NULL COMMENT '1: good, 2: warning, 3: serious' AFTER `template_id`;");

        DB::statement("ALTER TABLE `pm_reports`
CHANGE `process_status` `process_status` enum('good','warning','serious','na') COLLATE 'utf8mb4_unicode_ci' NOT NULL COMMENT '1: good, 2: warning, 3: serious' AFTER `template_id`;");

        DB::statement("ALTER TABLE `pm_reports`
CHANGE `customer_feedback_status` `customer_feedback_status` enum('good','warning','serious','na') COLLATE 'utf8mb4_unicode_ci' NOT NULL COMMENT '1: good, 2: warning, 3: serious' AFTER `template_id`;");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};
