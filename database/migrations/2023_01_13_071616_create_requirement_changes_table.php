<?php

use App\Enums\ERequirementChangeImpactedRole;
use App\Enums\ERequirementChangePriority;
use App\Enums\ERequirementChangeRequestBy;
use App\Enums\ERequirementChangeStatus;
use App\Enums\ERequirementChangeType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('requirement_change', function (Blueprint $table) {
            $table->id();
            $table->integer('stage_id')->nullable();
            $table->integer('project_id');
            $table->string('code')->nullable();
            $table->enum('type', ERequirementChangeType::getAll())->comment('1:fee, 2:bonus')->default(1);
            $table->enum('status', ERequirementChangeStatus::getAll())->comment('1:committed, 2:confirming, 3:done, 4:cancel, 5:paid')->default(1);
            $table->enum('priority', ERequirementChangePriority::getAll())->comment('1:high, 2:low')->default(1);
            $table->string('category')->nullable();
            $table->enum('request_by', ERequirementChangeRequestBy::getAll())->comment('1:team, 2:customer')->default(2);
            $table->date('request_date')->nullable();
            $table->string('title')->nullable();
            $table->string('content')->nullable();
            $table->string('detail_content')->nullable();
            $table->string('impact')->nullable();
            $table->enum('impacted_roles', ERequirementChangeImpactedRole::getAll())->nullable()->comment('1:all, 2:designer, 3:BA, 4:developer, 5:tester')->default(1);
            $table->date('release_date')->nullable();
            $table->decimal('cost', 9, 2)->nullable();
            $table->string('note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('requirement_change');
    }
};
