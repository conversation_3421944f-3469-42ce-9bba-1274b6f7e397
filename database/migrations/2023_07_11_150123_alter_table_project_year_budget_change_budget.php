<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::table('project_year_budget', function (Blueprint $table) {
            $table->float('budget', 10, 2)->unsigned(true)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
        Schema::table('project_year_budget', function (Blueprint $table) {
            $table->unsignedInteger('budget')->nullable()->change();
        });
    }
};
