<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->boolean('customer_type')->nullable()->after('rank');
            $table->tinyInteger('industry')->nullable()->after('customer_type');
            $table->string('language', 255)->nullable()->after('industry');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn('industry');
            $table->dropColumn('language');
            $table->dropColumn('customer_type');
        });
    }
};
