<?php

use App\Enums\EContractType;
use App\Enums\EProjectStatus;
use App\Enums\EProjectType;
use App\Enums\ERankType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->nullable();
            $table->enum('status', EProjectStatus::getAll())->default(1);
            $table->integer('division_id');
            $table->enum('contract_type', EContractType::getAll())->comment('1: labo, 2: fixed price')->default(2);
            $table->enum('project_type', EProjectType::getAll())->comment('1: by customer, 2: in-house, 3: start up, 4: in-house')->default(1);
            $table->float('billable');
            $table->float('budget');
            $table->enum('rank', ERankType::getAll())->comment('A, B, C, D');
            $table->longText('scope')->nullable();
            $table->longText('communication')->nullable();
            $table->longText('description')->nullable();
            $table->longText('contract_information')->nullable();
            $table->longText('critical')->nullable();
            $table->longText('note')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->timestamps();
            $table->index(['division_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('projects');
    }
};
