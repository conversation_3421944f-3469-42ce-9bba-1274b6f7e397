<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('functions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('project_id')->index();
            $table->unsignedBigInteger('function_category_id')->index();
            $table->unsignedBigInteger('sprint_id')->index();
            $table->string('name');
            $table->float('story_point')->nullable();
            $table->double('estimate')->nullable();
            $table->tinyInteger('work_completed')
                ->default(0)
                ->comment('0: N/A, 1: Closed, 2: Code done, 3: Test done')
                ->index();
            $table->text('note')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('functions');
    }
};
