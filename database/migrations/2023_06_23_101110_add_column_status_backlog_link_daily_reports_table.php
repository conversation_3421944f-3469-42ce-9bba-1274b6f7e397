<?php

use App\Enums\EDailyReport;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('daily_reports', function (Blueprint $table) {
            $table->boolean('status')->default(EDailyReport::DEFAULT_TOOL_TYPE)->after('actual_time');
            $table->mediumText('link_backlog')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('daily_reports', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('link_backlog');
        });
    }
};
