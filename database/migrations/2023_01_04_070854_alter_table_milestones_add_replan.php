<?php

use App\Enums\EReplanBy;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('milestones', function (Blueprint $table) {
            $table->longText('reason')->after('status')->nullable();
            $table->enum('replan_by', EReplanBy::getAll())->after('status')->nullable()->comment('1:by team, 2:by customer');
            $table->date('replan_to_date')->after('status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('milestones', function (Blueprint $table) {
            $table->dropColumn('reason');
            $table->dropColumn('replan_by');
            $table->dropColumn('replan_to_date');
        });
    }
};
