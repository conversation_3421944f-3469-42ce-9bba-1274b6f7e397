<?php

use App\Enums\EViolationReport;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('violation_reports', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->integer('creator_id');
            $table->integer('handler_id');
            $table->integer('rule_id')->nullable();
            $table->integer('violator_id')->nullable();
            $table->integer('project_id')->nullable();
            $table->integer('team_id')->nullable();
            $table->enum('status', EViolationReport::getStatus())->comment('1: processing, 2: processed, 3: reject')->default(1);
            $table->date('violation_date')->nullable();
            $table->longText('violation_content')->nullable();
            $table->longText('processing_content')->nullable();
            $table->string('evidence_url')->nullable();
            $table->string('evidence_file_name')->nullable();
            $table->string('result_url')->nullable();
            $table->string('result_file_name')->nullable();
            $table->string('result')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
        Schema::create('violation_report_penalties', function (Blueprint $table) {
            $table->id();
            $table->integer('penalty_id');
            $table->integer('violation_report_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('violation_reports');
        Schema::dropIfExists('violation_report_penalties');
    }
};
