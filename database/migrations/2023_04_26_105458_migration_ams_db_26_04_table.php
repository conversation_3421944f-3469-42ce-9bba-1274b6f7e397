<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('ALTER TABLE `allocations` CHANGE `role_id` `role_id` int NULL AFTER `updated_at`;');
        DB::statement('alter table customers modify email varchar(255) null;');
        DB::statement('alter table requirement_change modify status int null;');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
