<?php

use App\Enums\EReplanBy;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('stages', function (Blueprint $table) {
            $table->longText('reason')->after('end_date')->nullable();
            $table->enum('replan_by', EReplanBy::getAll())->after('end_date')->comment('1:by team, 2:by customer')->nullable();
            $table->date('replan_end_date')->after('end_date')->nullable();
            $table->date('replan_start_date')->after('end_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('stages', function (Blueprint $table) {
            $table->dropColumn('reason');
            $table->dropColumn('replan_by');
            $table->dropColumn('replan_end_date');
            $table->dropColumn('replan_start_date');
        });
    }
};
