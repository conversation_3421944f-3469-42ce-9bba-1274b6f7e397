<?php

use App\Enums\EEvaluateStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pm_reports', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id');
            $table->integer('project_id');
            $table->date('date');
            $table->integer('template_id')->nullable();
            $table->enum('cost_status', EEvaluateStatus::getAll())->comment('1: good, 2: warning, 3: serious');
            $table->longText('cost_comment')->nullable();
            $table->enum('quality_status', EEvaluateStatus::getAll())->comment('1: good, 2: warning,3: serious');
            $table->longText('quality_comment')->nullable();
            $table->enum('timeliness_status', EEvaluateStatus::getAll())->comment('1: good, 2: warning,3: serious');
            $table->longText('timeliness_comment')->nullable();
            $table->enum('process_status', EEvaluateStatus::getAll())->comment('1: good,2: warning,3: serious');
            $table->longText('process_comment')->nullable();
            $table->enum('customer_feedback_status', EEvaluateStatus::getAll())->comment('1: good,2: warning,3: serious');
            $table->longText('customer_feedback_comment')->nullable();
            $table->longText('note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pm_reports');
    }
};
