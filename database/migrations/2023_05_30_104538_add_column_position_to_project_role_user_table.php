<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('project_role_user', function (Blueprint $table) {
            $table->enum('position_in_project', \App\Enums\EPosition::POSITIONS_IN_PROJECT)
                ->comment("1: monitor, 2: supporter, 3: implementor")
                ->nullable()
                ->after('role_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('project_role_user', function (Blueprint $table) {
            $table->dropColumn('position_in_project');
        });
    }
};
