<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('quality_gates', function (Blueprint $table) {
            // Make sprint_id nullable if it isn't already
            $table->unsignedBigInteger('sprint_id')->nullable()->change();
            
            // Add unique constraint for project-level quality gates
            // This ensures only one quality gate per project when sprint_id is null
            $table->unique(['project_id'], 'unique_project_quality_gate_constraint');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('quality_gates', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique('unique_project_quality_gate_constraint');
            
            // Revert sprint_id to not nullable (if needed)
            // Note: This might fail if there are null values, so handle carefully
            // $table->unsignedBigInteger('sprint_id')->nullable(false)->change();
        });
    }
};
