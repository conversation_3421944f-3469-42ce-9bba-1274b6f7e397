<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('weekly_reports', function (Blueprint $table) {
            $table->id();
            $table->integer('project_id')->nullable();
            $table->date('week_start_date')->nullable();
            $table->tinyInteger('stage_type')->nullable();
            $table->integer('pm_id')->nullable();
            $table->integer('dm_id')->nullable();
            $table->integer('pqa_id')->nullable();
            $table->integer('pic_id')->nullable();
            $table->tinyInteger('pm_status')->nullable();
            $table->tinyInteger('dm_status')->nullable();
            $table->tinyInteger('pqa_status')->nullable();
            $table->tinyInteger('pic_status')->nullable();
            $table->string('pm_note', 1000)->nullable();
            $table->string('dm_note', 1000)->nullable();
            $table->string('pqa_note', 1000)->nullable();
            $table->string('pic_note', 1000)->nullable();
            $table->integer('action_id')->nullable();
            $table->tinyInteger('action_status')->nullable();
            $table->date('expiration_date')->nullable();
            $table->string('action_content', 1000)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('weekly_reports');
    }
};
