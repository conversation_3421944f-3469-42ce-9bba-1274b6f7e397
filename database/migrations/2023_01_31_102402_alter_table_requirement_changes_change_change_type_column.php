<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('requirement_change', function (Blueprint $table) {
            $table->decimal('cost', 7, 3)->nullable()->change();
            $table->text('content')->nullable()->change();
            $table->text('detail_content')->nullable()->change();
            $table->text('note')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('requirement_change', function (Blueprint $table) {
            $table->string('content')->nullable()->change();
            $table->string('detail_content')->nullable()->change();
            $table->decimal('cost', 9, 2)->nullable()->change();
            $table->string('note')->nullable()->change();
        });
    }
};
