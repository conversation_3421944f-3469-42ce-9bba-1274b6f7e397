<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('quality_gates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('project_id')->index();
            $table->unsignedBigInteger('sprint_id')->index();
            $table->tinyInteger('quality_gate')
                ->default(0)
                ->comment('0: N/A, 1: Fail, 2: Pass')
                ->index();
            $table->integer('number_of_non_compliance')->default(0);
            $table->integer('number_of_process')->default(0);
            $table->integer('number_of_incident')->default(0);
            $table->integer('number_of_customer_complaint')->default(0);
            $table->text('note')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('quality_gates');
    }
};
