<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \DB::statement("ALTER TABLE `deliverables` CHANGE `status` `status` enum('1','2') COLLATE 'utf8mb4_unicode_ci' NULL AFTER `expected_date`;");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \DB::statement("ALTER TABLE `deliverables` CHANGE `status` `status` enum('1','2') COLLATE 'utf8mb4_unicode_ci' NULL AFTER `expected_date`;");
    }
};
