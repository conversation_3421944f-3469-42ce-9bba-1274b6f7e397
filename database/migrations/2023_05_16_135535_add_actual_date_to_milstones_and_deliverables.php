<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('milestones', function (Blueprint $table) {
            $table->date('actual_date')->nullable()->after('replan_to_date');
        });
        Schema::table('deliverables', function (Blueprint $table) {
            $table->date('actual_date')->nullable()->after('replan_to_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('milestones', function (Blueprint $table) {
            $table->dropColumn('actual_date');
        });
        Schema::table('deliverables', function (Blueprint $table) {
            $table->dropColumn('actual_date');
        });
    }
};
