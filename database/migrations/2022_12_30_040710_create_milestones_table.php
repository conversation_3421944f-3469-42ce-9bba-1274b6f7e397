<?php

use App\Enums\EMilestoneAndDeliverableStatus;
use App\Enums\EMilestoneType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('milestones', function (Blueprint $table) {
            $table->id();
            $table->integer('stage_id');
            $table->enum('type', EMilestoneType::getAll())->comment('1:analysis, 2:design, 3:development, 4:testing')->nullable();
            $table->string('name');
            $table->date('expected_date');
            $table->enum('status', EMilestoneAndDeliverableStatus::getAll())->comment('1:open, 2:close')->nullable();
            $table->timestamps();
            $table->index(['stage_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('milestones');
    }
};
