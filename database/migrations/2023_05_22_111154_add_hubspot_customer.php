<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('hubspot_id')->nullable()->after('id');
            $table->string('city')->nullable()->after('phone');
            $table->string('domain')->nullable()->after('city');
            $table->string('industry')->nullable()->after('domain');
            $table->string('state')->nullable()->after('industry');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn(['city', 'domain', 'industry', 'state', 'hubspot_id']);
        });
    }
};
