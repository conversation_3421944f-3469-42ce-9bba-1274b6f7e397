<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \DB::statement("ALTER TABLE `projects` CHANGE `rank` `rank` ENUM('A','B','C', 'D') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;");
        Schema::table('projects', function (Blueprint $table) {
            $table->float('billable')->nullable()->change();
            $table->float('budget')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \DB::statement("ALTER TABLE `projects` CHANGE `rank` `rank` ENUM('A','B','C', 'D') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;");
        Schema::table('projects', function (Blueprint $table) {
            $table->float('billable')->nullable(false)->change();
            $table->float('budget')->nullable(false)->change();
        });
    }
};
