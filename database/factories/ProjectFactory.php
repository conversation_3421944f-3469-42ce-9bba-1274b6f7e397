<?php

namespace Database\Factories;

use App\Enums\EContractType;
use App\Enums\EProjectStatus;
use App\Enums\EProjectType;
use App\Enums\ERankType;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Project>
 */
class ProjectFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Project::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        static $number = 0;

        return [
            'name' => 'project '.$number,
            'code' => 'PJ'.sprintf('%05d', $number++),
            'status' => fake()->randomElement(EProjectStatus::getAll()),
            'division_id' => fake()->numberBetween(0, 3),
            'contract_type' => fake()->randomElement(EContractType::getAll()),
            'project_type' => fake()->randomElement(EProjectType::getAll()),
            'billable' => fake()->randomFloat(1, 0, 100),
            'budget' => fake()->randomFloat(1, 0, 100),
            'rank' => fake()->randomElement(ERankType::getAll()),
            'scope' => Str::random(50),
        ];
    }
}
